FROM node:22-alpine AS builder
ARG ENVIRONMENT=rel
WORKDIR /workspace

# 複製源代碼 (在安裝依賴後)
COPY . .
RUN echo "✅ Step 1: 複製源代碼完成"
RUN corepack enable

# 複製環境特定的配置檔案
RUN echo "🔧 使用環境: ${ENVIRONMENT}"
RUN cp /workspace/src/apps/web/env/.env.${ENVIRONMENT} /workspace/src/apps/web/.env || echo "⚠️  警告: src/apps/web/.env.${ENVIRONMENT} 不存在"
RUN cat /workspace/src/apps/web/.env
RUN echo "Environment files check completed"

# 本機測打包才需要複製Access Token檔案，檔案來源是cp ~/.npmrc .npmrc.docker (安裝mayo私有元件庫時會用到)
RUN cp /workspace/.npmrc.docker /root/.npmrc

WORKDIR /workspace/src
RUN pnpm i --frozen-lockfile
RUN echo "✅ Step 4: 依賴安裝完成"

RUN pnpm --filter=web build
RUN echo "✅ Step 5: 構建完成"

FROM nginx:1.28.0-alpine AS runner

COPY --from=builder /workspace/src/apps/web/dist /usr/share/nginx/html
COPY --from=builder /workspace/build/nginx/nginx.conf /usr/share/nginx/conf/nginx.conf

EXPOSE 8080

CMD ["nginx", "-c", "/usr/share/nginx/conf/nginx.conf", "-g", "daemon off;"]
