## 專案目錄結構

```text
├── .DS_Store
├── .cursorrules
├── .gitignore
├── .gitmessage
├── .npmrc.docker
├── README.md
├── docs
├── snapshot.md
├── snatshot.cjs
├── src
│   ├── .DS_Store
│   ├── .env
│   ├── .env.example
│   ├── .gitignore
│   ├── .langgraph_api
│   │   ├── .langgraphjs_api.checkpointer.json
│   │   ├── .langgraphjs_api.store.json
│   │   └── .langgraphjs_ops.json
│   ├── .npmrc
│   ├── README.md
│   ├── apps
│   │   ├── .DS_Store
│   │   ├── agents
│   │   │   ├── .prettierrc
│   │   │   ├── MCP_tools.md
│   │   │   ├── README.md
│   │   │   ├── eslint.config.js
│   │   │   ├── package.json
│   │   │   ├── src
│   │   │   │   ├── memory-agent
│   │   │   │   │   ├── README.md
│   │   │   │   │   ├── configuration.ts
│   │   │   │   │   ├── graph.ts
│   │   │   │   │   ├── prompts.ts
│   │   │   │   │   ├── state.ts
│   │   │   │   │   ├── static
│   │   │   │   │   │   ├── memories.png
│   │   │   │   │   │   └── memory_graph.png
│   │   │   │   │   ├── tests
│   │   │   │   │   │   ├── integration
│   │   │   │   │   │   │   └── graph.int.test.ts
│   │   │   │   │   │   └── unit
│   │   │   │   │   │       ├── configuration.test.ts
│   │   │   │   │   │       └── graph.test.ts
│   │   │   │   │   ├── tools.ts
│   │   │   │   │   └── utils.ts
│   │   │   │   ├── react-agent
│   │   │   │   │   ├── README.md
│   │   │   │   │   ├── configuration.ts
│   │   │   │   │   ├── graph.ts
│   │   │   │   │   ├── prompts.ts
│   │   │   │   │   ├── static
│   │   │   │   │   │   └── studio_ui.png
│   │   │   │   │   ├── tests
│   │   │   │   │   │   ├── integration
│   │   │   │   │   │   │   └── graph.int.test.ts
│   │   │   │   │   │   └── unit
│   │   │   │   │   │       └── graph.test.ts
│   │   │   │   │   ├── tools.ts
│   │   │   │   │   └── utils.ts
│   │   │   │   ├── research-agent
│   │   │   │   │   ├── README.md
│   │   │   │   │   ├── index-graph
│   │   │   │   │   │   ├── configuration.ts
│   │   │   │   │   │   ├── graph.ts
│   │   │   │   │   │   └── state.ts
│   │   │   │   │   ├── retrieval-graph
│   │   │   │   │   │   ├── configuration.ts
│   │   │   │   │   │   ├── graph.ts
│   │   │   │   │   │   ├── prompts.ts
│   │   │   │   │   │   ├── researcher-graph
│   │   │   │   │   │   │   ├── graph.ts
│   │   │   │   │   │   │   └── state.ts
│   │   │   │   │   │   ├── state.ts
│   │   │   │   │   │   └── utils.ts
│   │   │   │   │   ├── sample_docs.json
│   │   │   │   │   ├── shared
│   │   │   │   │   │   ├── configuration.ts
│   │   │   │   │   │   ├── retrieval.ts
│   │   │   │   │   │   ├── state.ts
│   │   │   │   │   │   └── utils.ts
│   │   │   │   │   └── static
│   │   │   │   │       └── studio_ui.png
│   │   │   │   └── retrieval-agent
│   │   │   │       ├── README.md
│   │   │   │       ├── configuration.ts
│   │   │   │       ├── graph.ts
│   │   │   │       ├── index_graph.ts
│   │   │   │       ├── prompts.ts
│   │   │   │       ├── retrieval.ts
│   │   │   │       ├── state.ts
│   │   │   │       ├── static
│   │   │   │       │   └── studio_ui.png
│   │   │   │       └── utils.ts
│   │   │   ├── tsconfig.json
│   │   │   └── turbo.json
│   │   └── web
│   │       ├── .DS_Store
│   │       ├── .env
│   │       ├── .gitignore
│   │       ├── .prettierrc
│   │       ├── README.md
│   │       ├── agent-chat-web.md
│   │       ├── components.json
│   │       ├── docs
│   │       │   ├── app
│   │       │   │   ├── CommandPayload-API.md
│   │       │   │   └── agent-chat-flow.md
│   │       │   ├── components
│   │       │   │   └── thread
│   │       │   │       └── 跨窗口通信與排班表下載流程.md
│   │       │   └── docker
│   │       │       └── README.md
│   │       ├── env
│   │       │   ├── .env.rel
│   │       │   ├── .env.tst
│   │       │   ├── .env.uat
│   │       │   └── README.md
│   │       ├── eslint.config.js
│   │       ├── index.html
│   │       ├── package.json
│   │       ├── pnpm-lock.yaml
│   │       ├── public
│   │       │   ├── logo.svg
│   │       │   └── xe_robot.png
│   │       ├── src
│   │       │   ├── App.css
│   │       │   ├── App.tsx
│   │       │   ├── components
│   │       │   │   ├── icons
│   │       │   │   │   ├── ai_icon.svg
│   │       │   │   │   ├── ai_icon_hover.svg
│   │       │   │   │   ├── github.tsx
│   │       │   │   │   ├── langgraph.tsx
│   │       │   │   │   ├── zoom_1.svg
│   │       │   │   │   └── zoom_2.svg
│   │       │   │   ├── thread
│   │       │   │   │   ├── agent-inbox
│   │       │   │   │   │   ├── components
│   │       │   │   │   │   │   ├── inbox-item-input.tsx
│   │       │   │   │   │   │   ├── state-view.tsx
│   │       │   │   │   │   │   ├── thread-actions-view.tsx
│   │       │   │   │   │   │   ├── thread-id.tsx
│   │       │   │   │   │   │   └── tool-call-table.tsx
│   │       │   │   │   │   ├── hooks
│   │       │   │   │   │   │   └── use-interrupted-actions.tsx
│   │       │   │   │   │   ├── index.tsx
│   │       │   │   │   │   ├── types.ts
│   │       │   │   │   │   └── utils.ts
│   │       │   │   │   ├── components
│   │       │   │   │   │   ├── ChatFooter.tsx
│   │       │   │   │   │   ├── ChatHeader.tsx
│   │       │   │   │   │   ├── QuickActionButtons.tsx
│   │       │   │   │   │   ├── ScrollToBottom.tsx
│   │       │   │   │   │   ├── StickyToBottomContent.tsx
│   │       │   │   │   │   ├── XELogoButton.tsx
│   │       │   │   │   │   └── index.ts
│   │       │   │   │   ├── history
│   │       │   │   │   │   └── index.tsx
│   │       │   │   │   ├── hooks
│   │       │   │   │   │   ├── index.ts
│   │       │   │   │   │   ├── useDownloadFlow.ts
│   │       │   │   │   │   ├── useFormSubmit.ts
│   │       │   │   │   │   ├── usePostRobotComm.ts
│   │       │   │   │   │   └── useScheduleData.ts
│   │       │   │   │   ├── index.tsx
│   │       │   │   │   ├── markdown-styles.css
│   │       │   │   │   ├── markdown-text.tsx
│   │       │   │   │   ├── messages
│   │       │   │   │   │   ├── ai.tsx
│   │       │   │   │   │   ├── generic-interrupt.tsx
│   │       │   │   │   │   ├── human.tsx
│   │       │   │   │   │   ├── shared.tsx
│   │       │   │   │   │   └── tool-calls.tsx
│   │       │   │   │   ├── syntax-highlighter.tsx
│   │       │   │   │   ├── tooltip-icon-button.tsx
│   │       │   │   │   └── utils.ts
│   │       │   │   ├── ui
│   │       │   │   │   ├── avatar.tsx
│   │       │   │   │   ├── button.tsx
│   │       │   │   │   ├── card.tsx
│   │       │   │   │   ├── input.tsx
│   │       │   │   │   ├── label.tsx
│   │       │   │   │   ├── password-input.tsx
│   │       │   │   │   ├── popover.tsx
│   │       │   │   │   ├── separator.tsx
│   │       │   │   │   ├── sheet.tsx
│   │       │   │   │   ├── shift
│   │       │   │   │   │   ├── docs
│   │       │   │   │   │   │   └── README.md
│   │       │   │   │   │   ├── shift-business-logic.ts
│   │       │   │   │   │   ├── shift-constants.ts
│   │       │   │   │   │   ├── shift-render-components.tsx
│   │       │   │   │   │   ├── shift-scheduler-export.tsx
│   │       │   │   │   │   ├── shift-scheduler-page.tsx
│   │       │   │   │   │   ├── shift-scheduler-template.tsx
│   │       │   │   │   │   └── shift-stats-components.tsx
│   │       │   │   │   ├── shift-settings-modal.tsx
│   │       │   │   │   ├── shift_setting
│   │       │   │   │   │   ├── README_Shift_Setting.md
│   │       │   │   │   │   ├── components
│   │       │   │   │   │   │   ├── ActionModal.tsx
│   │       │   │   │   │   │   ├── LoadingOverlay.tsx
│   │       │   │   │   │   │   ├── RosterModal.tsx
│   │       │   │   │   │   │   ├── ScheduleTable.tsx
│   │       │   │   │   │   │   └── index.ts
│   │       │   │   │   │   ├── constants.tsx
│   │       │   │   │   │   ├── hooks
│   │       │   │   │   │   │   └── useShift.ts
│   │       │   │   │   │   ├── services
│   │       │   │   │   │   │   ├── README.md
│   │       │   │   │   │   │   └── api.ts
│   │       │   │   │   │   ├── shift.tsx
│   │       │   │   │   │   ├── types.ts
│   │       │   │   │   │   └── utils
│   │       │   │   │   │       ├── data-transform.ts
│   │       │   │   │   │       └── helpers.ts
│   │       │   │   │   ├── skeleton.tsx
│   │       │   │   │   ├── sonner.tsx
│   │       │   │   │   ├── switch.tsx
│   │       │   │   │   ├── textarea.tsx
│   │       │   │   │   └── tooltip.tsx
│   │       │   │   └── version
│   │       │   ├── custom-elements
│   │       │   │   ├── LitApp.tsx
│   │       │   │   ├── chat-component.ts
│   │       │   │   └── index.ts
│   │       │   ├── datas
│   │       │   │   ├── api.json
│   │       │   │   └── api.types.ts
│   │       │   ├── generated
│   │       │   │   └── shift.tsx
│   │       │   ├── hooks
│   │       │   │   └── useMediaQuery.tsx
│   │       │   ├── index.css
│   │       │   ├── lib
│   │       │   │   ├── agent-inbox-interrupt.ts
│   │       │   │   ├── api-key.tsx
│   │       │   │   ├── constants.ts
│   │       │   │   ├── ensure-tool-responses.ts
│   │       │   │   ├── export-with-react.ts
│   │       │   │   ├── postRobot
│   │       │   │   │   └── index.ts
│   │       │   │   └── utils.ts
│   │       │   ├── main.tsx
│   │       │   ├── providers
│   │       │   │   ├── MessageCenterExample.tsx
│   │       │   │   ├── ParentData.tsx
│   │       │   │   ├── Stream.tsx
│   │       │   │   ├── Thread.tsx
│   │       │   │   └── client.ts
│   │       │   └── vite-env.d.ts
│   │       ├── tailwind.config.js
│   │       ├── tsconfig.app.json
│   │       ├── tsconfig.json
│   │       ├── tsconfig.node.json
│   │       ├── turbo.json
│   │       ├── vite.config.ts
│   │       └── vite.webcomponet.config.ts
│   ├── langgraph.json
│   ├── package.json
│   ├── pnpm-lock.yaml
│   ├── pnpm-workspace.yaml
│   ├── providers
│   ├── tsconfig.json
│   └── turbo.json
└── test
```

## 函式清單

### src/apps/agents/src/memory-agent/configuration.ts
- **ensureConfiguration(config?: LangGraphRunnableConfig)**

### src/apps/agents/src/memory-agent/tools.ts
- **initializeTools(config?: LangGraphRunnableConfig)**

### src/apps/agents/src/memory-agent/utils.ts
- **splitModelAndProvider(fullySpecifiedName: string)**

### src/apps/agents/src/research-agent/retrieval-graph/utils.ts
- **getMessageText(msg: BaseMessage)**
- **formatDoc(doc: Document)**
- **formatDocs(docs?: Document[])**

### src/apps/agents/src/research-agent/shared/utils.ts
- **formatDoc(doc: Document)**
- **formatDocs(docs?: Document[])**

### src/apps/agents/src/retrieval-agent/utils.ts
- **getMessageText(msg: BaseMessage)**
- **formatDoc(doc: Document)**
- **formatDocs(docs?: Document[])**

### src/apps/web/src/components/thread/agent-inbox/utils.ts
- **prettifyText(action: string)**
- **baseMessageObject(item: unknown)**
- **unknownToPrettyDate(input: unknown)**

### src/apps/web/src/components/thread/hooks/useDownloadFlow.ts
- **useDownloadFlow()**

### src/apps/web/src/components/thread/hooks/usePostRobotComm.ts
- **usePostRobotComm(messages: Message[])**
- **usePostRobotCommV2(messages: Message[])**
- **useEnhancedPostRobotComm(messages: Message[])**

### src/apps/web/src/components/thread/hooks/useScheduleData.ts
- **useScheduleData()**

### src/apps/web/src/components/thread/utils.ts
- **getContentString(content: Message["content"])**

### src/apps/web/src/components/ui/shift/shift-business-logic.ts
- **determineShiftStatus(dayCalendar: CalendarDay | undefined)**
- **getShiftCategory(calendarDay: CalendarDay)**
- **calculateTotalStats(employees: Employee[])**
- **calculateShiftWorkHours(shiftSchedule: ShiftSchedule | null)**
- **calculateDailyAttendanceCount(date: string, employees: any[])**

### src/apps/web/src/components/ui/shift/shift-constants.ts
- **convertApiDataToComponent(apiData: ShiftDataResponse)** - 從 API 資料轉換為組件所需格式的工具函數

### src/apps/web/src/components/ui/shift_setting/utils/helpers.ts
- **debounce(<T extends (...args: any[])** - 防抖函數

### src/apps/web/src/lib/ensure-tool-responses.ts
- **ensureToolCallsHaveResponses(messages: Message[])**

### src/apps/web/src/lib/export-with-react.ts
- **exportScheduleWithReact(options: ExportOptions = {})**

### src/apps/web/src/lib/utils.ts
- **cn(...inputs: ClassValue[])**

### src/apps/web/src/providers/client.ts
- **createClient(apiUrl: string, apiKey: string | undefined)**

## 依賴清單

## agents

### devDependencies
```json
{
  "@eslint/eslintrc": "^3.3.0",
  "@eslint/js": "^9.22.0",
  "@jest/globals": "^29.7.0",
  "@types/node": "^20",
  "@types/uuid": "^10.0.0",
  "@typescript-eslint/eslint-plugin": "^8.26.1",
  "@typescript-eslint/parser": "^8.26.1",
  "eslint": "^9.19.0",
  "eslint-config-prettier": "^10.1.1",
  "eslint-plugin-import": "^2.31.0",
  "eslint-plugin-no-instanceof": "^1.0.1",
  "eslint-plugin-prettier": "^5.2.3",
  "globals": "^15.14.0",
  "prettier": "^3.3.3",
  "tsx": "^4.19.1",
  "turbo": "latest",
  "typescript": "^5"
}
```

### dependencies
```json
{
  "@elastic/elasticsearch": "^8.17.1",
  "@langchain/anthropic": "^0.3.15",
  "@langchain/cohere": "^0.3.2",
  "@langchain/community": "^0.3.35",
  "@langchain/core": "^0.3.42",
  "@langchain/langgraph": "^0.2.55",
  "@langchain/langgraph-checkpoint": "^0.0.16",
  "@langchain/mcp-adapters": "^0.4.5",
  "@langchain/mongodb": "^0.1.0",
  "@langchain/openai": "^0.4.4",
  "@langchain/pinecone": "^0.2.0",
  "@n8n/json-schema-to-zod": "^1.1.0",
  "@pinecone-database/pinecone": "^5.1.1",
  "dotenv": "^16.4.5",
  "langchain": "^0.3.19",
  "mongodb": "^6.14.2",
  "uuid": "^10.0.0",
  "zod": "^3.23.8"
}
```

## web

### devDependencies
```json
{
  "@eslint/js": "^9.19.0",
  "@types/axios": "^0.14.4",
  "@types/lodash": "^4.17.16",
  "@types/node": "^22.13.5",
  "@types/post-robot": "^10.0.6",
  "@types/react": "^19.0.8",
  "@types/react-dom": "^19.0.3",
  "@types/react-syntax-highlighter": "^15.5.13",
  "@vitejs/plugin-react": "^4.3.4",
  "autoprefixer": "^10.4.20",
  "dotenv": "^16.4.7",
  "eslint": "^9.19.0",
  "eslint-config-prettier": "^10.1.1",
  "eslint-plugin-prettier": "^5.2.3",
  "eslint-plugin-react-hooks": "^5.0.0",
  "eslint-plugin-react-refresh": "^0.4.18",
  "globals": "^15.14.0",
  "prettier": "^3.5.3",
  "tailwind-scrollbar": "^4.0.1",
  "tailwindcss": "^4.0.6",
  "turbo": "latest",
  "typescript": "~5.7.2",
  "typescript-eslint": "^8.22.0",
  "vite": "^6.1.0",
  "vite-plugin-svgr": "^4.3.0"
}
```

### dependencies
```json
{
  "@langchain/core": "^0.3.42",
  "@langchain/langgraph": "^0.2.55",
  "@langchain/langgraph-api": "^0.0.16",
  "@langchain/langgraph-cli": "^0.0.16",
  "@langchain/langgraph-sdk": "^0.0.57",
  "@mayo/mayo-ui-beta": "2.1.12",
  "@radix-ui/react-avatar": "^1.1.3",
  "@radix-ui/react-dialog": "^1.1.6",
  "@radix-ui/react-label": "^2.1.2",
  "@radix-ui/react-popover": "^1.1.8",
  "@radix-ui/react-separator": "^1.1.2",
  "@radix-ui/react-slot": "^1.1.2",
  "@radix-ui/react-switch": "^1.1.3",
  "@radix-ui/react-tooltip": "^1.1.8",
  "@tailwindcss/postcss": "^4.0.9",
  "@tailwindcss/vite": "^4.0.9",
  "@vitejs/plugin-basic-ssl": "^2.0.0",
  "axios": "^1.10.0",
  "class-variance-authority": "^0.7.1",
  "clsx": "^2.1.1",
  "date-fns": "^4.1.0",
  "dayjs": "^1.11.13",
  "esbuild": "^0.25.0",
  "esbuild-plugin-tailwindcss": "^2.0.1",
  "framer-motion": "^12.4.9",
  "katex": "^0.16.21",
  "lit": "^3.3.0",
  "lodash": "^4.17.21",
  "lucide-react": "^0.476.0",
  "next-themes": "^0.4.4",
  "nuqs": "^2.4.1",
  "post-robot": "^8.0.32",
  "react": "^19.0.0",
  "react-dom": "^19.0.0",
  "react-markdown": "^10.0.1",
  "react-router-dom": "^6.17.0",
  "react-syntax-highlighter": "^15.5.0",
  "recharts": "^2.15.1",
  "rehype-katex": "^7.0.1",
  "remark-gfm": "^4.0.1",
  "remark-math": "^6.0.0",
  "sonner": "^2.0.1",
  "tailwind-merge": "^3.0.2",
  "tailwindcss-animate": "^1.0.7",
  "use-stick-to-bottom": "^1.0.46",
  "uuid": "^11.0.5",
  "zod": "^3.24.2"
}
```

## agent-chat-app

### devDependencies
```json
{
  "turbo": "latest",
  "tsx": "^4.19.1",
  "typescript": "^5",
  "eslint": "^9.19.0",
  "concurrently": "^9.1.2",
  "@typescript-eslint/eslint-plugin": "^8.26.1",
  "@eslint/eslintrc": "^3.3.0",
  "@typescript-eslint/parser": "^8.26.1",
  "@tsconfig/recommended": "^1.0.8",
  "eslint-config-prettier": "^10.1.1",
  "eslint-plugin-import": "^2.31.0",
  "eslint-plugin-no-instanceof": "^1.0.1",
  "eslint-plugin-prettier": "^5.2.3",
  "prettier": "^3.3.3"
}
```

### dependencies
無

