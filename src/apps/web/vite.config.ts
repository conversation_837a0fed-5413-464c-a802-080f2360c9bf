import path from 'path';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';
import basicSsl from '@vitejs/plugin-basic-ssl';
import svgr from 'vite-plugin-svgr';

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss(), basicSsl(), svgr()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    host: '0.0.0.0', // 监听所有网络接口，而不仅仅是localhost
    port: 3000,
    allowedHosts: ['dev.mayohr.com', 'tst-apolloxe.mayohr.com'], // 允许这个域名访问
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    proxy: {
      // 代理 API 請求，自動處理 HttpOnly cookie
      '/api/proxy': {
        target: 'https://test-xe-langgraph-proxy-vm.mayohr.com',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/api\/proxy/, ''),
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req) => {
            console.log('\n🚀 ========== 代理請求開始 ==========');
            console.log(`📋 請求方法: ${req.method}`);
            console.log(`🎯 原始 URL: ${req.url}`);
            console.log(`🌐 代理目標: ${options.target}${proxyReq.path}`);

            // 輸出原始請求 headers
            console.log('📤 原始請求 Headers:');
            Object.entries(req.headers).forEach(([key, value]) => {
              console.log(`   ${key}: ${value}`);
            });

            // 讀取 HttpOnly cookie '__ModuleSessionCookie'
            const cookies = req.headers.cookie;
            if (cookies) {
              const cookieMatch = cookies.match(/__ModuleSessionCookie=([^;]+)/);
              if (cookieMatch && cookieMatch[1]) {
                const authToken = cookieMatch[1];
                console.log(
                  '🔑 從 HttpOnly Cookie 讀取到 Auth Token:',
                  authToken.substring(0, 20) + '...'
                );

                // 將 cookie 值轉換為 Authorization header
                proxyReq.setHeader('Authorization', `Bearer ${authToken}`);

                // 檢查 Content-Type，對於 FormData 請求不覆蓋
                const contentType = req.headers['content-type'];
                if (req.method === 'PUT' || req.method === 'POST') {
                  if (contentType && contentType.includes('multipart/form-data')) {
                    console.log('📁 檢測到 FormData 請求，保持原始 Content-Type');
                    console.log(`📁 FormData Content-Type: ${contentType}`);
                  } else if (!contentType || contentType.includes('application/json')) {
                    proxyReq.setHeader('Content-Type', 'application/json');
                    console.log('📝 設置 Content-Type 為 application/json');
                  }
                }

                console.log('✅ 已添加 Authorization header 到代理請求');
              } else {
                console.warn('⚠️  未找到 __ModuleSessionCookie');
              }
            } else {
              console.warn('⚠️  請求中沒有 cookies');
            }

            // 輸出最終發送的 headers
            console.log('📤 最終代理請求 Headers:');
            Object.entries(proxyReq.getHeaders()).forEach(([key, value]) => {
              console.log(`   ${key}: ${value}`);
            });

            // 對於 FormData 請求，嘗試輸出資料資訊
            if (req.headers['content-type']?.includes('multipart/form-data')) {
              console.log('📁 FormData 請求詳情:');
              console.log(`   Content-Length: ${req.headers['content-length'] || 'unknown'}`);
              console.log(`   Content-Type: ${req.headers['content-type']}`);

              // 如果有 boundary，提取並顯示
              const boundaryMatch = req.headers['content-type']?.match(/boundary=([^;]+)/);
              if (boundaryMatch) {
                console.log(`   Boundary: ${boundaryMatch[1]}`);
              }
            }

            console.log('🚀 ========== 代理請求結束 ==========\n');
          });

          proxy.on('proxyRes', (proxyRes, req) => {
            console.log('\n📡 ========== 代理回應開始 ==========');
            console.log(`📋 請求: ${req.method} ${req.url}`);
            console.log(`📊 狀態碼: ${proxyRes.statusCode}`);
            console.log(`📝 狀態訊息: ${proxyRes.statusMessage}`);

            // 輸出回應 headers
            console.log('📥 回應 Headers:');
            Object.entries(proxyRes.headers).forEach(([key, value]) => {
              console.log(`   ${key}: ${value}`);
            });

            console.log('📡 ========== 代理回應結束 ==========\n');
          });

          proxy.on('error', (err, req) => {
            console.error('\n❌ ========== 代理錯誤 ==========');
            console.error(`🚫 錯誤訊息: ${err.message}`);
            console.error(`📋 請求: ${req?.method} ${req?.url}`);
            console.error(`📊 錯誤碼: ${(err as any).code || 'unknown'}`);
            console.error('❌ ========== 代理錯誤結束 ==========\n');
          });
        },
      },
    },
  },
});
