# Docker 配置說明文件

## 概述

本項目使用 Docker 來部署 Web 應用，採用 Nginx 作為 Web 服務器。主要包含兩個核心配置文件：

1. **Dockerfile** - Docker 鏡像構建配置
2. **nginx.conf** - Nginx 服務器配置

## Dockerfile 分析

### 現有配置

```dockerfile
FROM nginx:latest
ARG APP_PATH=apps/web

RUN ls -l

COPY ${APP_PATH}/dist /usr/share/nginx/html
COPY config/nginx/nginx.conf /usr/share/nginx/conf/nginx.conf

EXPOSE 80

CMD ["nginx", "-c", "/usr/share/nginx/conf/nginx.conf", "-g", "daemon off;"]
```

### 配置說明

| 項目 | 說明 | 備註 |
|------|------|------|
| `FROM nginx:latest` | 使用官方 Nginx 最新版本作為基礎鏡像 | 建議指定具體版本號 |
| `ARG APP_PATH=apps/web` | 定義構建參數，指定應用路徑 | 可在構建時覆蓋 |
| `RUN ls -l` | 列出目錄內容 | 調試用途，生產環境可移除 |
| `COPY ${APP_PATH}/dist` | 複製編譯後的前端文件到容器 | 需確保 dist 目錄存在 |
| `COPY config/nginx/nginx.conf` | 複製自定義 Nginx 配置 | 注意路徑映射 |
| `EXPOSE 80` | 聲明容器監聽端口 | 與 nginx.conf 中的端口不一致 |
| `CMD` | 啟動 Nginx 服務 | 使用自定義配置文件 |

### 問題與改進建議

1. **端口不一致**：Dockerfile 暴露 80 端口，但 nginx.conf 監聽 3000 端口
2. **版本固定**：建議使用具體版本號而非 latest
3. **多階段構建**：缺少構建階段，需要預先編譯 dist 目錄

## nginx.conf 分析

### 核心配置

#### 1. 基礎設置
```nginx
user nginx;
worker_processes auto;  # 自動檢測 CPU 核心數
```

#### 2. 事件配置
```nginx
events {
    worker_connections 1024;  # 每個 worker 最大連接數
}
```

#### 3. HTTP 配置

**MIME 類型定義**：
- 支援多種文件類型（HTML、CSS、JS、圖片、視頻等）
- 確保瀏覽器正確解析文件類型

**服務器配置**：
```nginx
server {
    listen       3000;           # 監聽端口
    server_name  localhost;      # 服務器名稱
}
```

#### 4. 壓縮設置
```nginx
gzip  on;                      # 啟用 gzip 壓縮
gzip_min_length 1k;            # 最小壓縮文件大小
gzip_comp_level 5;             # 壓縮級別 (1-9)
gzip_types text/plain ...;     # 壓縮的文件類型
gzip_disable "MSIE [1-6]\.";   # 禁用舊版 IE 的 gzip
gzip_vary on;                  # 添加 Vary: Accept-Encoding 頭
```

#### 5. 路由配置

**健康檢查端點**：
```nginx
location /health {
    access_log off;            # 不記錄健康檢查日誌
    return 200 "healthy\n";    # 返回 200 狀態
    add_header Content-Type text/plain;
}
```

**主要服務**：
```nginx
location / {
    root /usr/share/nginx/html;              # 靜態文件根目錄
    index index.php index.html index.htm;    # 默認首頁文件
    add_header Access-Control-Allow-Origin *; # CORS 設置
    
    # 靜態資源緩存設置
    if ( $request_uri ~* ^.+.(js|css|jpg|png|...)$ ){
        add_header Cache-Control max-age=7776000;  # 90天緩存
        add_header Access-Control-Allow-Origin *;
    }
}
```

## 使用指南

### 1. 構建鏡像

```bash
# 在項目根目錄執行
docker build -f config/docker/Dockerfile -t agent-chat-web .

# 或使用自定義應用路徑
docker build -f config/docker/Dockerfile --build-arg APP_PATH=apps/web -t agent-chat-web .
```

### 2. 運行容器

```bash
# 標準運行
docker run -d -p 3000:3000 --name agent-chat-web agent-chat-web

# 開發環境運行（掛載日誌）
docker run -d -p 3000:3000 \
  -v $(pwd)/logs:/var/log/nginx \
  --name agent-chat-web \
  agent-chat-web
```

### 3. 健康檢查

```bash
# 檢查服務狀態
curl http://localhost:3000/health

# 應該返回：healthy
```

## 故障排除
1. **端口衝突**：確保 Dockerfile EXPOSE 與 nginx.conf listen 端口一致
2. **文件不存在**：檢查 `apps/web/dist` 目錄是否存在並包含編譯後的文件
3. **權限問題**：確保 nginx 用戶有權限訪問靜態文件
4. **配置文件錯誤**：使用 `nginx -t` 測試配置文件語法

### 調試命令

```bash
# 查看容器日誌
docker logs agent-chat-web

# 進入容器調試
docker exec -it agent-chat-web /bin/sh

# 測試 nginx 配置
docker exec agent-chat-web nginx -t
``` 