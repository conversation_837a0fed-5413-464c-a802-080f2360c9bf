# 跨窗口通信與排班表下載流程說明

## 📝 概述

本文檔說明 Agent Chat App 中實現的跨窗口通信機制，以及如何整合聊天資料與排班表模板來實現智能下載功能。

## 🏗️ 架構概覽

```
┌─────────────────┐    PostRobot    ┌─────────────────┐
│                 │ ◄─────────────► │                 │
│  Thread 組件     │                 │ Template 組件    │
│  (父窗口)        │                 │ (子窗口)        │
│                 │                 │                 │
│ • 聊天介面       │                 │ • 排班表模板     │
│ • 下載控制       │                 │ • 資料處理       │
│ • 資料提取       │                 │ • 監聽回應       │
└─────────────────┘                 └─────────────────┘
         │                                   │
         │                                   │
         ▼                                   ▼
┌─────────────────┐                 ┌─────────────────┐
│                 │                 │                 │
│ export-with-    │                 │ 資料提取與       │
│ react.ts        │                 │ 格式化處理       │
│                 │                 │                 │
│ • HTML 生成     │                 │ • AI 內容解析   │
│ • 檔案下載      │                 │ • 員工資訊提取   │
│ • 統一導出      │                 │ • 標題識別       │
└─────────────────┘                 └─────────────────┘
```

## 🔄 完整資料流程

### 1. 下載觸發階段

**位置**: `src/apps/web/src/components/thread/index.tsx`

```typescript
// 用戶點擊「下載當月班表」按鈕
const downloadWithChatData = useCallback(() => {
  console.log('開始下載流程：請求子窗口資料...');
  setIsWaitingForChildData(true);
  
  // 向子窗口發送請求，要求它處理並回傳資料
  sendMessage(window, 'request-processed-data', {
    messages: messages,
    timestamp: new Date().toISOString()
  });
}, [messages]);
```

**流程說明**:
- Thread 組件設置等待狀態
- 發送 `request-processed-data` 消息到子窗口
- 傳遞當前的聊天記錄 `messages`

### 2. 子窗口資料處理階段

**位置**: `src/apps/web/src/components/ui/shift/shift-scheduler-template.tsx`

```typescript
// 監聽父窗口的資料處理請求
const processRequestListener = setupListener('request-processed-data', (data) => {
  console.log('收到父窗口的資料處理請求:', data);
  
  // 處理資料並回傳
  if (data && data.messages && Array.isArray(data.messages)) {
    const processedData = processMessagesForSchedule(data.messages);
    
    // 回傳處理後的資料給父窗口
    sendMessage(window.parent, 'processed-data-response', {
      processedData: {
        shifts: processedData.generatedShifts || [],
        employees: processedData.extractedEmployees,
        title: processedData.updatedTitle
      },
      timestamp: new Date().toISOString()
    });
  }
});
```

**處理邏輯**:
```typescript
const processMessagesForSchedule = (messages: any[]) => {
  const result = { messages };
  
  // 遍歷 messages 尋找 AI 生成的排班資料
  for (const message of messages) {
    if (message.type === 'ai' && message.content) {
      const extractedInfo = extractScheduleInfoFromContent(message.content);
      if (extractedInfo.shifts) result.generatedShifts = extractedInfo.shifts;
      if (extractedInfo.employees) result.extractedEmployees = extractedInfo.employees;
      if (extractedInfo.title) result.updatedTitle = extractedInfo.title;
    }
  }
  
  return result;
};
```

### 3. 父窗口接收與下載階段

**位置**: `src/apps/web/src/components/thread/index.tsx`

```typescript
// 監聽子窗口回傳的處理後資料
const processedDataListener = setupListener('processed-data-response', (data) => {
  console.log('收到子窗口處理後的資料:', data);
  setIsWaitingForChildData(false);
  
  if (data.processedData) {
    // 使用子窗口處理後的資料進行下載
    quickExportReact.custom({
      currentMonth: dayjs(),
      shifts: data.processedData.shifts || generateTestShifts(dayjs().year(), dayjs().month() + 1),
      employees: data.processedData.employees,
      title: data.processedData.title || `AI 生成排班表 - ${dayjs().format('YYYY年MM月')}`
    });
    console.log('使用子窗口處理後的資料完成下載！');
  }
});
```

## 🛠️ 技術實現細節

### PostRobot 通信

使用 `@/lib/postRobot` 實現跨窗口通信：

```typescript
// 發送消息
export const sendMessage = async (
  targetWindow: Window,
  messageType: string,
  messageData: any
): Promise<void> => {
  const response = await postRobot.send(targetWindow, messageType, messageData);
};

// 設置監聽器
export const setupListener = (
  messageType: string,
  callback: (data: any) => void
) => {
  return postRobot.on(messageType, (event) => {
    callback(event.data);
    return Promise.resolve({ status: 'Received successfully' });
  });
};
```

### 資料提取邏輯

從 AI 聊天內容中提取排班資訊：

```typescript
const extractScheduleInfoFromContent = (content: string) => {
  const result = {};
  
  // 提取標題
  const titleMatch = content.match(/排班表[：:]\s*(.+)/);
  if (titleMatch) {
    result.title = titleMatch[1].trim();
  }
  
  // 提取員工資訊
  const employeeMatches = content.match(/員工[：:]\s*(.+)/g);
  if (employeeMatches) {
    const extractedEmployees = [];
    employeeMatches.forEach((match, index) => {
      const name = match.replace(/員工[：:]\s*/, '').trim();
      extractedEmployees.push({
        id: `extracted-${index}`,
        name
      });
    });
    result.employees = extractedEmployees;
  }
  
  return result;
};
```

### 統一導出機制

使用現有的 `export-with-react.ts` 避免代碼重複：

```typescript
// 位置: src/apps/web/src/lib/export-with-react.ts
export const quickExportReact = {
  custom: (options: ExportOptions) => {
    exportScheduleWithReact(options);
  }
};

export const exportScheduleWithReact = (options: ExportOptions = {}) => {
  const htmlContent = renderToStaticMarkup(
    React.createElement(ShiftSchedulerExport, options)
  );
  
  const fullHtmlContent = `<!DOCTYPE html>\n${htmlContent}`;
  const blob = new Blob([fullHtmlContent], { type: 'text/html;charset=utf-8' });
  
  // 創建下載連結並觸發下載
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  link.click();
};
```

## 📋 消息類型說明

| 消息類型 | 方向 | 用途 | 資料格式 |
|---------|------|------|----------|
| `request-processed-data` | Thread → Template | 請求處理聊天資料 | `{ messages: Array, timestamp: string }` |
| `processed-data-response` | Template → Thread | 回傳處理後資料 | `{ processedData: Object, timestamp: string }` |
| `request-schedule-data` | Template → Thread | 請求原始聊天資料 | `{ requestType: string, source: Window }` |
| `schedule-data-response` | Thread → Template | 回應原始聊天資料 | `{ messages: Array, timestamp: string }` |

## 🎯 功能特點

### ✅ 同步執行
- 整個下載流程在 `downloadWithChatData` 函數作用域內完成
- 使用 Promise-based 的 PostRobot 確保消息傳遞順序

### ✅ 錯誤處理
```typescript
if (data.processedData) {
  // 使用子窗口處理的資料
} else {
  // 回退到本地提取的資料
  const chatData = extractScheduleDataFromMessages();
  quickExportReact.custom({ /* 使用本地資料 */ });
}
```

### ✅ 用戶體驗
- 下載按鈕顯示處理狀態
- 防止重複點擊
- 清晰的載入動畫

```typescript
<Button disabled={isLoading || isWaitingForChildData}>
  {isWaitingForChildData ? (
    <>
      <LoaderCircle className="w-4 h-4 mr-1 animate-spin" />
      處理中...
    </>
  ) : (
    <>
      <Download className="w-4 h-4 mr-1" />
      下載當月班表
    </>
  )}
</Button>
```

## 🔧 使用方式

### 啟用跨窗口通信

在 Template 組件中：
```typescript
<ShiftSchedulerTemplate 
  enableParentDataListener={true}
  title="我的排班表"
  // 其他 props...
/>
```

### 觸發下載

在 Thread 組件中，用戶點擊下載按鈕即可觸發完整流程。

## 📁 相關檔案

```
src/apps/web/src/
├── components/
│   ├── thread/
│   │   └── index.tsx                 # Thread 組件 (主要邏輯)
│   └── ui/shift/
│       ├── shift-scheduler-template.tsx  # Template 組件
│       └── shift-constants.ts        # 類型定義
├── lib/
│   ├── postRobot/
│   │   └── index.ts                  # 跨窗口通信庫
│   └── export-with-react.ts          # 統一導出功能
```

## 🎉 總結

這個實現提供了一個完整的跨窗口通信解決方案，能夠：

1. **智能提取** AI 聊天中的排班資訊
2. **無縫整合** 聊天介面與排班模板
3. **統一管理** 所有下載功能
4. **優雅處理** 錯誤和異常情況
5. **提供良好** 的用戶體驗

通過這個架構，我們成功避免了代碼重複，保持了系統的可維護性和擴展性。 