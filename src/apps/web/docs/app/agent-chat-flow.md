
### 主要交互流程 (頂部圖表 - 從 "使用者" 開始)

1. **使用者** 在 `Thread` 元件中輸入訊息並提交。
    
2. `Thread` 元件捕獲提交事件。
    
3. 系統創建一個新的**人類訊息物件**。
    
4. 透過 `stream.submit()` 函數，將訊息以及任何當前的**工具調用狀態**發送到 LangGraph 伺服器。
    
5. **LangGraph 伺服器** 接收並處理請求。
    
6. 伺服器開始以**流式**方式返回數據。
    
7. `StreamProvider` 元件接收流式數據並更新應用程式的**訊息狀態**。
    
8. 狀態的更新會觸發使用者介面 (UI) **重新渲染**，從而向使用者展示 AI 的回覆內容。
    
9. 系統判斷是否需要**進入中斷流程** (例如，AI 需要使用者提供更多資訊或確認)。
    
    - **是**：如果需要中斷，則展示 `ThreadView` 元件，等待使用者進行交互。
        
    - **否**：如果不需要中斷，則流程繼續。
        
10. **使用者完成交互** (如果發生了中斷)。
    
11. 系統**恢復正常對話流程**。
    

#### 完整通信流程


```mermaid
flowchart TD
    AA["StreamProvider 元件：<br>初始化連接 (apiUrl, apiKey, assistantId)<br>可能涉及 GET/POST /threads"] --> BB{使用者輸入};
    BB --> CC["handleSubmit()"];
    CC --> DD["stream.submit()<br>封裝並發送訊息"];
    DD --> EE["LangGraph 伺服器<br>POST /threads/{threadId}/runs/stream"];
    EE --> FF[流式響應];
    FF --> GG[StreamProvider：<br>接收並更新狀態];
    GG --> HH[觸發 UI 更新];
    HH --> II{檢測到中斷？};
    II -- 是 --> JJ["useInterruptedActions()<br>渲染 ThreadView"];
    JJ --> KK[使用者交互];
    KK --> LL["恢復流程<br>可能涉及 POST /threads/{threadId}/runs/{runId}/resume"];
    II -- 否 --> LL_End[結束/繼續等待輸入];
    LL --> LL_End;


    style AA fill:#lightgrey,stroke:#333,stroke-width:1px
    style EE fill:#ccf,stroke:#333,stroke-width:2px

    classDef userNode fill:#f9f,stroke:#333,stroke-width:2px;
    classDef serverNode fill:#ccf,stroke:#333,stroke-width:2px;
    classDef processNode fill:#e6ffe6,stroke:#333,stroke-width:1px;
    classDef decisionNode fill:#ffe4b5,stroke:#333,stroke-width:1px;
    classDef ioNode fill:#add8e6,stroke:#333,stroke-width:1px;
    classDef apiNode fill:#cce5ff,stroke:#333,stroke-width:1px;

    class BB,CC,DD,FF,GG,HH,JJ,KK,LL,LL_End processNode;
    class II decisionNode;
    class EE apiNode;
```

這部分更詳細地展示了前端應用程式與 LangGraph 伺服器之間的通信機制。

1. **初始化連接**：
    
    - `StreamProvider` 元件負責初始化與 LangGraph 服務的連接。
        
    - 它使用 `apiUrl` (API 網址)、`apiKey` (API 金鑰) 和 `assistantId` (助手 ID) 來連接到特定的 LangGraph 服務實例。此階段可能涉及調用 `GET/POST /threads` 端點來創建或獲取線程。
        
2. **發送訊息流程**：
    
    - **使用者輸入**訊息。
        
    - 觸發 `handleSubmit()` 函數。
        
    - `stream.submit()` 函數將訊息封裝並發送到 LangGraph 伺服器的 `POST /threads/{threadId}/runs/stream` 端點。
        
3. **接收響應流程**：
    
    - **LangGraph 伺服器** 處理請求後，以**流式響應**的方式將數據傳回。
        
    - `StreamProvider` 接收這些流式數據，並相應地**更新應用程式的狀態**。
        
    - 狀態的改變會**觸發 UI 更新**，將 AI 的回覆顯示給使用者。
        
4. **中斷處理流程**：
    
    - 系統**檢測到中斷**信號。
        
    - `useInterruptedActions()` 鉤子 (hook) 被調用，並渲染 `ThreadView` 元件。
        
    - `ThreadView` 元件允許**使用者進行交互** (例如，回答問題、提供輸入)。
        
    - 使用者完成交互後，流程**重新恢復**。
        

這個流程圖應該能幫助您更好地理解 Agent Chat Web 應用程式的內部工作原理，特別是它如何與後端 AI 服務進行通信和處理使用者交互。