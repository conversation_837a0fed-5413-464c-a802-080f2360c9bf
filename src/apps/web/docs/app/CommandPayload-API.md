## 概述

CommandPayload 系統是 AI 聊天應用中用於處理對話額外執行命令的核心機制。它允許在正常的對話流程之外，觸發特定的系統行為和業務邏輯。

### 核心功能
- 🔄 **命令分類管理**: 統一管理不同類型的系統命令
- 🎯 **類型安全**: 基於 TypeScript 的強類型約束
- 📡 **異步執行**: 支援在對話過程中異步執行業務邏輯
- 🔌 **可擴展性**: 易於添加新的命令類型

## 類型定義

### CommandType 枚舉

```typescript
export enum CommandType {
  // 刷新命令 - 觸發系統刷新操作
  REFRESH = 'refresh',
  // AI歡迎語命令 - 初始化對話並觸發歡迎消息
  GREETING_SHIFT_SCHEDULE = 'greeting-shift-schedule',
}
```

### CommandPayload 接口

```typescript
export interface CommandPayload {
  type: CommandType;    // 命令類型，必須為預定義的枚舉值
  payload?: any;        // 可選的命令參數，根據命令類型而定
}
```

## 命令類型詳解

### REFRESH 命令

**用途**: 觸發系統或特定組件的刷新操作

**技術實現**:
```typescript
const refreshCommand: CommandPayload = {
  type: CommandType.REFRESH,
  payload: undefined  // 通常不需要額外參數
}
```

**觸發時機**:
- AI 完成特定業務操作後
- 需要同步前端狀態時
- 排班數據更新完成後

### GREETING_SHIFT_SCHEDULE 命令

**用途**: 初始化對話並觸發 AI 歡迎語，通常攜帶排班相關的上下文數據

**技術實現**:
```typescript
const greetingCommand: CommandPayload = {
  type: CommandType.GREETING_SHIFT_SCHEDULE,
  payload: {
    departmentId: string,
    departmentName: string,
    companyName: string,
    scheduleStartDate: string,
    scheduleEndDate: string,
  }
}
```

**觸發時機**:
- 用戶首次進入聊天界面
- 從父視窗獲得排班數據後
- 需要重新初始化對話上下文時

## 交互流程設計

### 消息處理流程

```
1. 消息接收 → 2. 命令檢測 → 3. 命令執行 → 4. 狀態更新
     ↓              ↓              ↓              ↓
  Message      CommandPayload   Business      UI Refresh
  Object       Extraction       Logic         
```

### 詳細執行步驟

#### 1. 消息監聽與檢測
```typescript
const lastMessage: (Message & { command?: CommandPayload }) | undefined =
  messages[messages.length - 1];

if (lastMessage?.command?.type === CommandType.REFRESH) {
  // 執行刷新邏輯
}
```

#### 2. 命令分發機制
```typescript
useEffect(() => {
  const lastMessage = messages[messages.length - 1];
  const command = lastMessage?.command;
  
  if (!command) return;
  
  switch (command.type) {
    case CommandType.REFRESH:
      handleRefreshCommand(command.payload);
      break;
    case CommandType.GREETING_SHIFT_SCHEDULE:
      handleGreetingCommand(command.payload);
      break;
    default:
      console.warn('Unknown command type:', command.type);
  }
}, [messages]);
```

#### 3. 業務邏輯執行
```typescript
const handleRefreshCommand = (payload?: any) => {
  console.log('排班成功');
  sendMessage(window.parent, 'schedule-refresh', {});
};

const handleGreetingCommand = (payload?: any) => {
  formSubmit.handleSubmitWithWelcomeMessage('', 'ai', {
    type: CommandType.GREETING_SHIFT_SCHEDULE,
    payload: payload
  });
};
```

## 對話流程根據命令的行為模式

### REFRESH 命令流程
```
AI 完成操作 → 返回 REFRESH 命令 → 前端檢測命令 → 發送刷新事件給父視窗 → 父視窗更新界面
```

### GREETING_SHIFT_SCHEDULE 命令流程
```
頁面初始化 → 獲取父視窗數據 → 生成 GREETING 命令 → AI 處理歡迎邏輯 → 顯示歡迎消息
```

## 技術實現細節

### 消息擴展機制

原始的 `Message` 類型通過交集類型擴展：
```typescript
Message & { command?: CommandPayload }
```

這種設計保持了對原有消息系統的兼容性，同時添加了命令處理能力。

### 類型安全保證

1. **枚舉約束**: `CommandType` 確保只能使用預定義的命令
2. **接口定義**: `CommandPayload` 提供結構化的命令格式
3. **可選負載**: `payload?` 允許命令攜帶或不攜帶額外數據

### 錯誤處理

```typescript
try {
  // 命令執行邏輯
} catch (error) {
  console.error('Command execution failed:', error);
  // 錯誤回退邏輯
}
```

## 擴展指南

### 添加新命令類型

1. **更新枚舉**:
```typescript
export enum CommandType {
  REFRESH = 'refresh',
  GREETING_SHIFT_SCHEDULE = 'greeting-shift-schedule',
  NEW_COMMAND = 'new-command',  // 新增命令
}
```

2. **實現處理邏輯**:
```typescript
case CommandType.NEW_COMMAND:
  handleNewCommand(command.payload);
  break;
```

3. **添加業務處理函數**:
```typescript
const handleNewCommand = (payload?: any) => {
  // 新命令的業務邏輯
};
```

### 設計規範

- **命令名稱**: 使用 `SCREAMING_SNAKE_CASE` 格式
- **值約定**: 使用小寫連字符格式 (`kebab-case`)
- **向後兼容**: 避免刪除已有命令，使用 `@deprecated` 標記
- **文檔更新**: 每個新命令都需要更新此文檔

## 最佳實踐

1. **命令檢測**: 總是檢查命令是否存在再執行
2. **錯誤處理**: 為每個命令添加適當的錯誤處理
3. **日誌記錄**: 記錄命令執行的關鍵信息用於調試
4. **性能考慮**: 避免在命令處理中執行重量級操作

## 相關文件

- `src/apps/web/src/lib/constants.ts` - 命令定義
- `src/apps/web/src/components/thread/index.tsx` - 命令處理邏輯
- `src/apps/web/src/providers/Stream.tsx` - 消息流管理 