import path from "path";
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";
import basicSsl from '@vitejs/plugin-basic-ssl'
import svgr from 'vite-plugin-svgr'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss(), basicSsl(), svgr()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    host: '0.0.0.0', // 监听所有网络接口，而不仅仅是localhost
    port: 5173,
    allowedHosts: ['dev.mayohr.com', 'tst-apolloxe.mayohr.com'], // 允许这个域名访问
    headers: {
      'Access-Control-Allow-Origin': '*',
    }
  },
  build: {
    lib: {
      entry: './src/custom-elements/index.ts',
      name: 'MyWebComponents',
      formats: ['umd', 'iife'],
      fileName: (format) => `my-web-components.${format}.js`
    },
    rollupOptions: {
    },
    // 生成类型声明文件
    outDir: 'dist',
    emptyOutDir: true,
    target: 'es2015'
  },
  define: {
    // 在瀏覽器中模擬 process.env
    'process.env': {
      NODE_ENV: JSON.stringify('production')
    }
  }
});
