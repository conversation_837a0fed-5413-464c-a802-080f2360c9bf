# 環境變數配置目錄

此目錄包含不同環境的配置檔案。

## 檔案結構

```
env/
├── README.md                    # 此說明檔案
├── .env.example                # 環境變數範例檔案
├── .env.development            # 開發環境配置
├── .env.tst                # 測試環境配置
└── .env.rel            # 生產環境配置
```

## 使用方式

1. 複製 `.env.example` 並重新命名為對應環境
2. 填入實際的環境變數值
3. 在 Docker 構建時指定環境：

```bash
# 開發環境
docker build --build-arg ENVIRONMENT=development -t agent-chat-app:dev .

# 測試環境  
docker build --build-arg ENVIRONMENT=tst -t agent-chat-app:staging .

# 生產環境 (預設)
docker build -t agent-chat-app:prod .
```

## 安全提醒

- 🚨 **請勿將實際的環境變數檔案提交到版本控制**
- 🔒 **生產環境的敏感資訊應使用安全的方式管理**
- ✅ **只有 `.env.example` 檔案應該被提交到 Git** 