{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "turbo build:internal --filter=web", "build:internal": "rm -rf dist && tsc -b && vite build", "lint": "eslint .", "format": "prettier --write .", "format:check": "prettier --check .", "preview": "vite preview"}, "dependencies": {"@langchain/core": "^0.3.42", "@langchain/langgraph": "^0.2.55", "@langchain/langgraph-api": "^0.0.16", "@langchain/langgraph-cli": "^0.0.16", "@langchain/langgraph-sdk": "^0.0.57", "@mayo/mayo-ui-beta": "2.1.12", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.8", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/postcss": "^4.0.9", "@tailwindcss/vite": "^4.0.9", "@vitejs/plugin-basic-ssl": "^2.0.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "esbuild": "^0.25.0", "esbuild-plugin-tailwindcss": "^2.0.1", "framer-motion": "^12.4.9", "katex": "^0.16.21", "lit": "^3.3.0", "lodash": "^4.17.21", "lucide-react": "^0.476.0", "next-themes": "^0.4.4", "nuqs": "^2.4.1", "post-robot": "^8.0.32", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.0.1", "react-router-dom": "^6.17.0", "react-syntax-highlighter": "^15.5.0", "recharts": "^2.15.1", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "use-stick-to-bottom": "^1.0.46", "uuid": "^11.0.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/axios": "^0.14.4", "@types/lodash": "^4.17.16", "@types/node": "^22.13.5", "@types/post-robot": "^10.0.6", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "dotenv": "^16.4.7", "eslint": "^9.19.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "prettier": "^3.5.3", "tailwind-scrollbar": "^4.0.1", "tailwindcss": "^4.0.6", "turbo": "latest", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vite-plugin-svgr": "^4.3.0"}, "overrides": {"react-is": "^19.0.0-rc-69d4b800-20241021"}}