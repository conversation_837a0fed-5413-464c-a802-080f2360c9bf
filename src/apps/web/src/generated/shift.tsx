import React, { useState, useEffect, useCallback, useMemo, useRef, FC, JSX } from 'react';

// --- 型別定義 (Type Definitions) ---

// API 回應的原始資料結構
interface Worker {
  employee_id: string;
  employee_name: string;
}

interface GroupInfo {
  group_id: string;
  group_name: string;
  requirement: number[];
  workers: Worker[];
  note: string;
}

interface JsonData {
  time_slots: string[][];
  group_infos: GroupInfo[];
}

// 應用程式內部狀態的資料結構
interface StaffMember {
  id: string;
  name: string;
  employeeIds?: string;
}

interface Role {
  id: string;
  name: string;
  roster: string[]; // 儲存 StaffMember 的 id
  remarks: string;
  rosterText?: string; // 用於顯示的文字，由 useMemo 產生
}

interface Headcount {
  [roleId: string]: {
    [timeslot: string]: number;
  };
}

interface Schedule {
  roles: Role[];
  timeslots: string[];
  headcount: Headcount;
}

interface StaffData {
  [unitId: string]: StaffMember[];
}

interface StaffAssignments {
  [staffId: string]: string[];
}

interface Unit {
  id: string;
  name: string;
}

// Modal 相關型別
type ActionType = 'delete-role' | 'add-timeslot' | 'delete-timeslot';

interface BaseActionModalConfig {
  type: ActionType;
  title: string;
  message?: string;
  mode: 'confirm' | 'time-range';
  onConfirm: (type: ActionType, data: any) => void;
  onClose: () => void;
}

interface DeleteRoleAction extends BaseActionModalConfig {
  type: 'delete-role';
  data: Role;
}

interface DeleteTimeslotAction extends BaseActionModalConfig {
  type: 'delete-timeslot';
  data: string;
}

interface AddTimeslotAction extends BaseActionModalConfig {
  type: 'add-timeslot';
  data?: { start: string; end: string };
}

type ActionModalConfig =
  | DeleteRoleAction
  | DeleteTimeslotAction
  | AddTimeslotAction
  | {
      title: string;
      message: string;
      onConfirm: () => void;
      onClose: () => void;
    };

// --- API 設定 (請在此處替換成您的正式設定) ---
const API_CONFIG = {
  BASE_URL: 'https://test-xe-langgraph-proxy-vm.mayohr.com', // 您的 API 基礎網址
  REQUIREMENTS_ENDPOINT: '/uat/pbff/api/sections/requirements', // 需求的端點路徑
  AUTH_TOKEN:
    'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ijg1ZTZlNmJlM2MzMDBiOTk5MzFlY2MxZDA3NWYxNWFkIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.S3eoGQu5PXHp2nV2xYzVn2aD6lylnrb8lS4NZjSKdtk1V3Y1a6h-MMeUDR0Z6GGXNvPKHhCJf2gRQf4DcSQZYm4vRtSW3gL7oszv--_9vECZ5yaHKLKhsp5SX3lojI90ARAkB3e1bqOcjlhIy3B2QfQjMGWrCkyn3L14v6I6M2WO6OgTC1jSzQBLjHNUZXMbeDmQkTrSkRVHhOXVGbPuJLZn6FEW_Bsuke5ervfIO4uThmunFgH73SIofiXxtTn2Tqm2XWPirT7LuWSP_xfwOLL6ZY9is5vM_Unlu34YOT_MbO5tmaUlQJ7nRQ-m6FR8Jeae47JS88RVuR3quREYwg',
};

// 將前端的 unit ID 映射到後端的 section ID
const UNIT_ID_MAPPING: { [key: string]: string } = {
  kitchen: 'e36bcd83-5340-4ba9-a940-f2890eb0af92',
  front_house: 'YOUR_FRONT_HOUSE_UNIT_ID_HERE', // **待填**：請填入「外場」對應的 ID
};

// --- 靜態資料與常數 (來自使用者提供的資料) ---
const USER_PROVIDED_JSON: JsonData = {
  time_slots: [
    ['08:00', '09:00'],
    ['09:00', '10:00'],
    ['10:00', '11:00'],
    ['11:00', '12:00'],
    ['12:00', '13:00'],
    ['13:00', '14:00'],
    ['14:00', '15:00'],
    ['15:00', '16:00'],
    ['16:00', '17:00'],
    ['17:00', '18:00'],
    ['18:00', '19:00'],
    ['19:00', '20:00'],
    ['20:00', '21:00'],
    ['21:00', '22:00'],
    ['22:00', '23:00'],
    ['23:00', '00:00'],
  ],
  group_infos: [
    {
      group_id: 'd81049aa-eb02-4033-b963-132720a4612e',
      group_name: '主管',
      requirement: [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
      workers: [
        { employee_id: '7e9817f6-44d5-4836-a204-1116481d76fc', employee_name: '陳韋州' },
        { employee_id: 'd3ab95e3-4ecf-43d2-bc97-4b515031e0c2', employee_name: '林湘豐' },
      ],
      note: '＊陳韋州 Willy、林湘豐 Jerry不能同時休假\n＊目前主管只有兩個人，所以採輪休，保持最少1人需求',
    },
    {
      group_id: '28273912-f831-42b5-9bda-932f6e1a6758',
      group_name: '廚房',
      requirement: [2, 2, 2, 3, 3, 3, 3, 2, 2, 1, 1, 1, 1, 1, 1, 0],
      workers: [
        { employee_id: '102eccf0-5d95-4ffb-855e-a0728974ebef', employee_name: '黄于玲' },
        { employee_id: '83c5d489-43d6-41b7-ac21-3b528f9d5060', employee_name: '王大偉' },
        { employee_id: 'c0609f09-c79e-45e8-b450-fec1426a031d', employee_name: '曾子容' },
        { employee_id: '74d1598c-eda2-4c69-a2b2-40e375b61659', employee_name: '蔡佩頴' },
        { employee_id: '5705f90a-f423-460b-b875-ee6c926fe048', employee_name: '李佳憲' },
        { employee_id: '97e727e6-ed4e-4765-9a6c-884cec3dba0a', employee_name: '張維中' },
      ],
      note: '＊黄于玲 Irin、王大偉 David不能同時休假',
    },
  ],
};

const MOCK_DATA: { units: Unit[] } = {
  units: [
    { id: 'kitchen', name: '內場' },
    { id: 'front_house', name: '外場' },
  ],
};
const START_OF_DAY = '06:00';

const ICONS: { [key: string]: JSX.Element } = {
  delete: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M3 6h18" />
      <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" />
      <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
    </svg>
  ),
  bigSpinner: (
    <svg
      className="animate-spin h-10 w-10 text-blue-600 mx-auto"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      ></circle>
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
  ),
};

// --- Helper Functions ---
const getTimeValue = (time: string): number => {
  if (!time) return 0;
  const timeMoment = parseInt(time.replace(':', ''), 10);
  const startMoment = parseInt(START_OF_DAY.replace(':', ''), 10);
  return timeMoment < startMoment ? timeMoment + 2400 : timeMoment;
};

// --- Components ---

interface LoadingOverlayProps {
  text: string;
}
const LoadingOverlay: FC<LoadingOverlayProps> = ({ text }) => (
  <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-xl z-50">
    <div className="text-center">
      {ICONS.bigSpinner}
      <p className="mt-2 text-slate-600 font-medium">{text}</p>
    </div>
  </div>
);

interface ScheduleTableProps {
  schedule: Schedule;
  onUpdate: (
    roleId: string,
    field: 'roleName' | 'remarks' | 'headcount',
    value: string | number,
    timeslot?: string | null
  ) => void;
  onShowRosterModal: (role: Role) => void;
  onShowActionModal: (
    config: Omit<ActionModalConfig, 'onConfirm' | 'onClose' | 'title' | 'message' | 'mode'> & {
      type: ActionType;
      data?: any;
    }
  ) => void;
}
const ScheduleTable: FC<ScheduleTableProps> = React.memo(
  ({ schedule, onUpdate, onShowRosterModal, onShowActionModal }) => {
    const sortedTimeslots = useMemo(
      () =>
        (schedule.timeslots || [])
          .slice()
          .sort((a, b) => getTimeValue(a.split('-')[0]) - getTimeValue(b.split('-')[0])),
      [schedule.timeslots]
    );

    const handleInputChange = (
      roleId: string,
      field: 'roleName' | 'remarks' | 'headcount',
      value: string | number,
      timeslot: string | null = null
    ) => {
      onUpdate(roleId, field, value, timeslot);
    };

    return (
      <div
        className="table-container w-full overflow-auto rounded-lg border border-slate-200"
        style={{ maxHeight: '65vh' }}
      >
        <table
          className="w-full text-sm text-slate-700 relative"
          style={{ minWidth: '1200px', borderCollapse: 'separate', borderSpacing: 0 }}
        >
          <thead className="sticky top-0 z-20">
            <tr>
              <th className="sticky left-0 top-0 z-30 p-2 font-semibold text-slate-600 bg-slate-200 border-b border-r border-slate-300">
                群組/時間
              </th>
              {sortedTimeslots.map((slot) => (
                <th
                  key={slot}
                  className="p-2 font-semibold text-slate-600 bg-slate-50 border-b border-slate-300"
                >
                  {slot}
                  <button
                    onClick={() => onShowActionModal({ type: 'delete-timeslot', data: slot })}
                    className="delete-timeslot-btn text-slate-400 hover:text-red-500 ml-1"
                  >
                    ×
                  </button>
                </th>
              ))}
              <th className="p-2 font-semibold text-slate-600 bg-slate-50 border-b border-slate-300">
                指派名單
              </th>
              <th className="p-2 font-semibold text-slate-600 bg-slate-50 border-b border-slate-300">
                備註
              </th>
            </tr>
          </thead>
          <tbody>
            {(schedule.roles || []).map((role) => (
              <tr key={role.id} data-role-id={role.id} className="group hover:bg-slate-50">
                <td className="sticky left-0 z-10 p-0 bg-white group-hover:bg-slate-50 border-b border-r border-slate-200">
                  <div className="flex items-center gap-2 p-2">
                    <input
                      type="text"
                      value={role.name}
                      onChange={(e) => handleInputChange(role.id, 'roleName', e.target.value)}
                      className="role-name-input w-24 p-1 rounded border border-transparent hover:border-slate-300 focus:border-indigo-500 focus:outline-none bg-transparent"
                      autoComplete="off"
                    />
                    <button
                      onClick={() => onShowActionModal({ type: 'delete-role', data: role })}
                      className="text-slate-400 hover:text-red-500"
                    >
                      ×
                    </button>
                  </div>
                </td>
                {sortedTimeslots.map((slot) => {
                  const count = schedule.headcount?.[role.id]?.[slot] || 0;
                  return (
                    <td key={slot} className="p-1 border-b border-slate-200">
                      <input
                        type="number"
                        min="0"
                        value={count}
                        onChange={(e) =>
                          handleInputChange(
                            role.id,
                            'headcount',
                            parseInt(e.target.value) || 0,
                            slot
                          )
                        }
                        className="headcount-input w-[60px] text-center rounded-md border border-slate-300 p-1 focus:border-indigo-500 focus:ring-indigo-500"
                      />
                    </td>
                  );
                })}
                <td className="p-2 border-b border-slate-200">
                  <div className="flex items-center justify-center gap-2">
                    <span className="roster-text truncate max-w-xs" title={role.rosterText}>
                      {role.rosterText || '未指派'}
                    </span>
                    <button
                      onClick={() => onShowRosterModal(role)}
                      className="text-indigo-600 hover:text-indigo-800 font-bold"
                    >
                      編輯
                    </button>
                  </div>
                </td>
                <td className="p-1 border-b border-slate-200">
                  <input
                    type="text"
                    value={role.remarks || ''}
                    onChange={(e) => handleInputChange(role.id, 'remarks', e.target.value)}
                    className="remarks-input w-48 p-1 rounded border border-transparent hover:border-slate-300 focus:border-indigo-500 focus:outline-none bg-transparent"
                    autoComplete="off"
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  }
);

interface RosterModalProps {
  isOpen: boolean;
  onClose: () => void;
  role: Role | null;
  unitId: string;
  onSave: (roleId: string, roster: string[]) => void;
  staffData: StaffData;
  staffAssignments: StaffAssignments;
}
const RosterModal: FC<RosterModalProps> = ({
  isOpen,
  onClose,
  role,
  unitId,
  onSave,
  staffData,
  staffAssignments,
}) => {
  const [assignedIds, setAssignedIds] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (role) {
      setAssignedIds(new Set(role.roster || []));
    }
    setSearchTerm('');
  }, [isOpen, role]);

  const staffSorter = useCallback((a: StaffMember, b: StaffMember) => {
    if (a.name && b.name) {
      return a.name.localeCompare(b.name, 'zh-Hant');
    }
    return 0;
  }, []);

  const filteredAndSortedStaff = useMemo(() => {
    const allStaffForUnit = staffData[unitId] || [];
    return allStaffForUnit
      .filter((s) => {
        const term = searchTerm.toLowerCase();
        if (!term) return true;
        const nameMatch = s.name.toLowerCase().includes(term);
        const employeeIdMatch = s.employeeIds?.toLowerCase().includes(term);
        const assignedRoles = staffAssignments[s.id] || [];
        const roleMatch = assignedRoles.some((roleName) => roleName.toLowerCase().includes(term));
        return nameMatch || employeeIdMatch || roleMatch;
      })
      .sort(staffSorter);
  }, [staffData, unitId, searchTerm, staffAssignments, staffSorter]);

  const availableStaff = filteredAndSortedStaff.filter((s) => !assignedIds.has(s.id));
  const assignedStaff = filteredAndSortedStaff.filter((s) => assignedIds.has(s.id));

  if (!isOpen || !role) return null;

  const toggleStaff = (staffId: string) => {
    setAssignedIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(staffId)) newSet.delete(staffId);
      else newSet.add(staffId);
      return newSet;
    });
  };

  const handleSave = () => {
    onSave(role.id, Array.from(assignedIds));
    onClose();
  };

  interface StaffItemProps {
    staff: StaffMember;
    isAssigned: boolean;
    assignments: StaffAssignments;
  }
  const StaffItem: FC<StaffItemProps> = ({ staff, isAssigned, assignments }) => {
    const assignedRoles = (assignments[staff.id] || []).filter(
      (roleName) => roleName !== role.name
    );
    return (
      <div
        onClick={() => toggleStaff(staff.id)}
        className={`flex items-center justify-between p-2 rounded-md ${isAssigned ? 'bg-indigo-100' : 'bg-white'} cursor-pointer hover:bg-slate-100 transition`}
      >
        <div className="flex flex-col text-left gap-1">
          <div className="flex items-center gap-2 flex-wrap">
            <span className="font-semibold">{staff.name}</span>
            {assignedRoles.map((roleName) => (
              <span
                key={roleName}
                className="text-xs font-semibold bg-slate-200 text-slate-700 px-1.5 py-0.5 rounded-full"
              >
                {roleName}
              </span>
            ))}
          </div>
        </div>
        <span className="text-xl font-bold text-indigo-500">{isAssigned ? '−' : '+'}</span>
      </div>
    );
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-xl shadow-2xl w-full max-w-3xl transform transition-transform duration-300"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="px-6 py-4 border-b border-slate-200">
          <h3 className="text-lg font-bold text-slate-800">指派成員到 "{role.name}"</h3>
          <div className="mt-4">
            <input
              type="text"
              placeholder="搜尋姓名、群組..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </div>
        <div
          className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6"
          style={{ height: '400px', maxHeight: '60vh' }}
        >
          <div className="flex flex-col min-h-0">
            <h4 className="font-semibold text-slate-600 mb-2">單位所有成員 (可選)</h4>
            <div className="flex-grow overflow-y-auto border border-slate-200 rounded-lg p-2 space-y-2 bg-slate-50">
              {availableStaff.map((s) => (
                <StaffItem key={s.id} staff={s} isAssigned={false} assignments={staffAssignments} />
              ))}
            </div>
          </div>
          <div className="flex flex-col min-h-0">
            <h4 className="font-semibold text-slate-600 mb-2">已指派成員</h4>
            <div className="flex-grow overflow-y-auto border border-slate-200 rounded-lg p-2 space-y-2 bg-slate-50">
              {assignedStaff.map((s) => (
                <StaffItem key={s.id} staff={s} isAssigned={true} assignments={staffAssignments} />
              ))}
            </div>
          </div>
        </div>
        <div className="px-6 py-4 bg-slate-50 rounded-b-xl flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-5 py-2 bg-white border border-slate-300 text-slate-700 font-semibold rounded-lg hover:bg-slate-100"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="px-5 py-2 bg-indigo-600 text-white font-semibold rounded-lg shadow-md hover:bg-indigo-700"
          >
            確認儲存
          </button>
        </div>
      </div>
    </div>
  );
};

interface ActionModalProps {
  config: ActionModalConfig | null;
  onConfirm: (type: ActionType, data: any) => void;
  onClose: () => void;
}
const ActionModal: FC<ActionModalProps> = ({ config, onConfirm, onClose }) => {
  const [startTime, setStartTime] = useState('10:00');
  const [endTime, setEndTime] = useState('11:00');

  useEffect(() => {
    if (!config || !('mode' in config) || config.mode !== 'time-range') return;

    const data = config.data as { start: string; end: string } | undefined;
    setStartTime(data?.start || '10:00');
    setEndTime(data?.end || '11:00');
  }, [config]);

  if (!config) return null;

  const handleConfirm = () => {
    if (!('type' in config)) {
      config.onConfirm();
      return;
    }

    let result: any;
    switch (config.type) {
      case 'add-timeslot':
        result = { start: startTime, end: endTime };
        break;
      default:
        result = config.data;
    }
    onConfirm(config.type, result);
  };

  const { title, message } = config;
  const mode = 'mode' in config ? config.mode : 'confirm';

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[60]"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-xl shadow-2xl w-full max-w-md"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6">
          <h3 className="text-lg font-bold text-slate-800 mb-4">{title}</h3>
          {message && <p className="text-sm text-slate-600 mb-4">{message}</p>}
          {mode === 'time-range' && (
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-slate-700">開始時間</label>
                <input
                  type="time"
                  step="3600"
                  value={startTime}
                  onChange={(e) => setStartTime(e.target.value)}
                  className="mt-1 w-full form-input"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700">結束時間</label>
                <input
                  type="time"
                  step="3600"
                  value={endTime}
                  onChange={(e) => setEndTime(e.target.value)}
                  className="mt-1 w-full form-input"
                />
              </div>
            </div>
          )}
        </div>
        <div className="px-6 py-4 bg-slate-50 rounded-b-xl flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-5 py-2 bg-white border border-slate-300 text-slate-700 font-semibold rounded-lg hover:bg-slate-100"
          >
            取消
          </button>
          <button
            onClick={handleConfirm}
            className="px-5 py-2 bg-indigo-600 text-white font-semibold rounded-lg shadow-md hover:bg-indigo-700"
          >
            確認
          </button>
        </div>
      </div>
    </div>
  );
};

// --- Data Transformation Logic ---
const transformJsonToState = (
  jsonData: JsonData,
  unitId: string
): { schedule: Schedule; staff: StaffData } => {
  if (!jsonData || !jsonData.time_slots || !jsonData.group_infos) {
    console.warn('Loaded JSON data is invalid or empty. Returning empty schedule.');
    return { schedule: { roles: [], timeslots: [], headcount: {} }, staff: {} };
  }

  const newTimeslots = jsonData.time_slots.map((ts) => ts.join('-'));
  const newRoles: Role[] = [];
  const newHeadcount: Headcount = {};
  const allStaffMap = new Map<string, StaffMember>();
  let staffCounter = 0;

  jsonData.group_infos.forEach((group) => {
    const roleId = group.group_id || `role_${Date.now()}_${staffCounter++}`;
    const roster: string[] = [];

    (group.workers || []).forEach((worker) => {
      if (worker.employee_id && !allStaffMap.has(worker.employee_id)) {
        allStaffMap.set(worker.employee_id, {
          id: worker.employee_id,
          name: worker.employee_name,
          employeeIds: worker.employee_id,
        });
      }
      if (worker.employee_id) roster.push(worker.employee_id);
    });

    newRoles.push({
      id: roleId,
      name: group.group_name,
      roster: roster,
      remarks: group.note || '',
    });

    newHeadcount[roleId] = {};
    newTimeslots.forEach((slot, index) => {
      newHeadcount[roleId][slot] = group.requirement[index] || 0;
    });
  });

  const newStaffForUnit = Array.from(allStaffMap.values());

  return {
    schedule: {
      roles: newRoles,
      timeslots: newTimeslots,
      headcount: newHeadcount,
    },
    staff: { [unitId]: newStaffForUnit },
  };
};

// --- 主應用程式組件 ---
export default function App() {
  // --- State ---
  const [currentUnitId, setCurrentUnitId] = useState<string>('kitchen');
  const [currentSchedule, setCurrentSchedule] = useState<Schedule | null>(null);
  const [staffData, setStaffData] = useState<StaffData>({});
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [loadingText, setLoadingText] = useState<string>('讀取設定...');
  const [saveStatus, setSaveStatus] = useState<{ msg: string; visible: boolean; isError: boolean }>(
    { msg: '', visible: false, isError: false }
  );
  const [isRosterModalOpen, setIsRosterModalOpen] = useState<boolean>(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [actionModalConfig, setActionModalConfig] = useState<ActionModalConfig | null>(null);

  const debounceTimer = useRef<NodeJS.Timeout | null>(null);
  const units: Unit[] = MOCK_DATA.units;

  // --- Actions & Handlers ---
  const showSaveStatus = useCallback((msg: string, isError: boolean = false) => {
    setSaveStatus({ msg, visible: true, isError });
    if (debounceTimer.current) clearTimeout(debounceTimer.current);
    if (!isError) {
      debounceTimer.current = setTimeout(() => {
        setSaveStatus((prev) => ({ ...prev, visible: false }));
      }, 3000);
    }
  }, []);

  const closeModal = () => setActionModalConfig(null);

  const loadData = useCallback(async () => {
    const sectionId = UNIT_ID_MAPPING[currentUnitId];
    if (!sectionId || sectionId.startsWith('YOUR')) {
      showSaveStatus(`尚未設定 ${currentUnitId} 的單位 ID`, true);
      const { schedule, staff } = transformJsonToState(USER_PROVIDED_JSON, currentUnitId);
      setCurrentSchedule(schedule);
      setStaffData(staff);
      return;
    }

    setIsLoading(true);
    setLoadingText('讀取設定...');
    try {
      const API_URL = `${API_CONFIG.BASE_URL}${API_CONFIG.REQUIREMENTS_ENDPOINT}/${sectionId}`;
      const response = await fetch(API_URL, {
        method: 'GET',
        headers: { Authorization: `Bearer ${API_CONFIG.AUTH_TOKEN}` },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API 請求失敗: ${response.status} ${errorText}`);
      }

      const data: JsonData = await response.json();

      if (data) {
        const { schedule, staff } = transformJsonToState(data, currentUnitId);
        setCurrentSchedule(schedule);
        setStaffData(staff);
        showSaveStatus('✓ 已從 API 讀取設定');
      } else {
        throw new Error('從 API 獲取的資料格式不正確或為空');
      }
    } catch (error) {
      console.error('讀取資料時發生錯誤: ', error);
      const { schedule, staff } = transformJsonToState(USER_PROVIDED_JSON, currentUnitId);
      setCurrentSchedule(schedule);
      setStaffData(staff);
      showSaveStatus(`API 讀取失敗: ${(error as Error).message}。已載入預設資料。`, true);
    } finally {
      setIsLoading(false);
    }
  }, [currentUnitId, showSaveStatus]);

  // Effect for Initial Data Load
  useEffect(() => {
    loadData();
  }, [loadData]);

  // --- Memoized Data ---
  const scheduleWithRosterText = useMemo((): Schedule | null => {
    if (!currentSchedule || !currentSchedule.roles) return currentSchedule;
    const allStaffMap = new Map<string, StaffMember>(
      Object.values(staffData)
        .flat()
        .map((s) => [s.id, s])
    );
    return {
      ...currentSchedule,
      roles: currentSchedule.roles.map((role) => ({
        ...role,
        rosterText: (role.roster || []).map((id) => allStaffMap.get(id)?.name || '未知').join(', '),
      })),
    };
  }, [currentSchedule, staffData]);

  const staffAssignments = useMemo((): StaffAssignments => {
    const assignments: StaffAssignments = {};
    if (currentSchedule && currentSchedule.roles) {
      currentSchedule.roles.forEach((role) => {
        (role.roster || []).forEach((staffId) => {
          if (!assignments[staffId]) assignments[staffId] = [];
          assignments[staffId].push(role.name);
        });
      });
    }
    return assignments;
  }, [currentSchedule]);

  const handleSaveSettings = useCallback(async () => {
    const sectionId = UNIT_ID_MAPPING[currentUnitId];
    if (!sectionId || sectionId.startsWith('YOUR')) {
      setActionModalConfig({
        title: '錯誤',
        message: `尚未設定 ${currentUnitId} 的單位 ID，無法儲存。`,
        onConfirm: closeModal,
        onClose: closeModal,
      });
      return;
    }

    if (!currentSchedule) {
      setActionModalConfig({
        title: '錯誤',
        message: '沒有可儲存的資料。',
        onConfirm: closeModal,
        onClose: closeModal,
      });
      return;
    }
    setIsLoading(true);
    setLoadingText('儲存設定...');

    try {
      const time_slots = currentSchedule.timeslots.map((ts) => ts.split('-'));
      const allStaffMap = new Map(
        Object.values(staffData)
          .flat()
          .map((s) => [s.id, s])
      );

      const group_infos: GroupInfo[] = currentSchedule.roles.map((role) => {
        const workers = (role.roster || [])
          .map((staffId) => {
            const staffMember = allStaffMap.get(staffId);
            return staffMember
              ? { employee_id: staffMember.id, employee_name: staffMember.name }
              : null;
          })
          .filter((w): w is Worker => w !== null);

        const requirement = currentSchedule.timeslots.map(
          (slot) => currentSchedule.headcount?.[role.id]?.[slot] || 0
        );

        return {
          group_id: role.id,
          group_name: role.name,
          requirement: requirement,
          workers: workers,
          note: role.remarks || '',
        };
      });

      const outputJson: JsonData = { time_slots, group_infos };

      const API_URL = `${API_CONFIG.BASE_URL}${API_CONFIG.REQUIREMENTS_ENDPOINT}/${sectionId}`;
      const response = await fetch(API_URL, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${API_CONFIG.AUTH_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(outputJson),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API 請求失敗: ${response.status} ${errorText}`);
      }

      showSaveStatus('✓ 設定已儲存至伺服器');
    } catch (error) {
      console.error('儲存設定時發生錯誤: ', error);
      setActionModalConfig({
        title: '儲存失敗',
        message: (error as Error).message,
        onConfirm: closeModal,
        onClose: closeModal,
      });
    } finally {
      setIsLoading(false);
    }
  }, [currentSchedule, staffData, showSaveStatus, currentUnitId]);

  const handleScheduleUpdate = useCallback(
    (
      roleId: string,
      field: 'roleName' | 'remarks' | 'headcount' | 'roster',
      value: string | number | string[],
      timeslot: string | null = null
    ) => {
      setCurrentSchedule((prevSchedule) => {
        if (!prevSchedule) return null;
        const newSchedule = JSON.parse(JSON.stringify(prevSchedule)) as Schedule;
        const role = newSchedule.roles.find((r) => r.id === roleId);
        if (!role) return prevSchedule;

        if (field === 'headcount' && typeof value === 'number' && timeslot) {
          if (!newSchedule.headcount[roleId]) newSchedule.headcount[roleId] = {};
          newSchedule.headcount[roleId][timeslot] = value;
        } else if (field === 'roleName' && typeof value === 'string') {
          role.name = value;
        } else if (field === 'remarks' && typeof value === 'string') {
          role.remarks = value;
        } else if (field === 'roster' && Array.isArray(value)) {
          role.roster = value;
        }
        return newSchedule;
      });
    },
    []
  );

  const handleActionConfirm = useCallback(
    (type: ActionType, data: any) => {
      setCurrentSchedule((prevSchedule) => {
        if (!prevSchedule) return null;
        const newSchedule = JSON.parse(JSON.stringify(prevSchedule)) as Schedule;

        switch (type) {
          case 'delete-role':
            newSchedule.roles = newSchedule.roles.filter((r) => r.id !== (data as Role).id);
            if (newSchedule.headcount) delete newSchedule.headcount[(data as Role).id];
            break;
          case 'add-timeslot': {
            const { start, end } = data as { start: string; end: string };
            if (!start || !end || getTimeValue(start) >= getTimeValue(end)) {
              setActionModalConfig({
                title: '時間錯誤',
                message: '結束時間必須晚於開始時間。',
                onConfirm: () => showActionModal({ type: 'add-timeslot', data }),
                onClose: closeModal,
              });
              return prevSchedule; // Return previous state
            }
            const isOverlapping = (newSchedule.timeslots || []).some((slot) => {
              const [existingStart, existingEnd] = slot.split('-');
              return (
                getTimeValue(start) < getTimeValue(existingEnd) &&
                getTimeValue(existingStart) < getTimeValue(end)
              );
            });
            if (isOverlapping) {
              setActionModalConfig({
                title: '時間重疊',
                message: '您新增的時間段與現有時間重疊。',
                onConfirm: () => showActionModal({ type: 'add-timeslot', data }),
                onClose: closeModal,
              });
              return prevSchedule; // Return previous state
            }
            newSchedule.timeslots.push(`${start}-${end}`);
            break;
          }
          case 'delete-timeslot':
            newSchedule.timeslots = newSchedule.timeslots.filter((s) => s !== data);
            if (newSchedule.headcount) {
              Object.values(newSchedule.headcount).forEach(
                (roleSlots) => delete roleSlots[data as string]
              );
            }
            break;
          default:
            break;
        }
        return newSchedule;
      });
      closeModal();
    },
    [] // Removed dependencies as they cause re-renders issues with modals
  );

  const showActionModal = useCallback(
    (
      config: Omit<ActionModalConfig, 'onConfirm' | 'onClose' | 'title' | 'message' | 'mode'> & {
        type: ActionType;
        data?: any;
      }
    ) => {
      let modalConfig: ActionModalConfig;
      const baseConfig = { onConfirm: handleActionConfirm, onClose: closeModal };

      if (config.type?.startsWith('delete')) {
        modalConfig = {
          ...baseConfig,
          type: config.type,
          data: config.data,
          title: '確認刪除',
          message: '確定要刪除此項目嗎？此操作無法復原。',
          mode: 'confirm',
        } as ActionModalConfig;
      } else if (config.type === 'add-timeslot') {
        const slots = (currentSchedule?.timeslots || [])
          .slice()
          .sort((a, b) => getTimeValue(a.split('-')[0]) - getTimeValue(b.split('-')[0]));
        const lastSlot = slots[slots.length - 1] || '08:00-09:00';
        const lastEndTime = lastSlot.split('-')[1];
        const nextStartHour = parseInt(lastEndTime.split(':')[0], 10);
        const formatTime = (h: number, m: number = 0) =>
          `${String(h % 24).padStart(2, '0')}:${String(m).padStart(2, '0')}`;

        modalConfig = {
          ...baseConfig,
          type: config.type,
          data: config.data || { start: lastEndTime, end: formatTime(nextStartHour + 1) },
          title: '新增時間段',
          mode: 'time-range',
        } as ActionModalConfig;
      } else {
        // This case should not happen with proper typing, but as a fallback
        return;
      }
      setActionModalConfig(modalConfig as ActionModalConfig);
    },
    [currentSchedule, handleActionConfirm]
  );

  const handleAddRole = () => {
    if (!currentSchedule) return;
    setCurrentSchedule((prevSchedule) => {
      if (!prevSchedule) return null;
      const newSchedule = JSON.parse(JSON.stringify(prevSchedule)) as Schedule;
      const newRoleName = `新群組 ${(newSchedule.roles?.length || 0) + 1}`;
      newSchedule.roles.push({
        id: 'role_' + Date.now(),
        name: newRoleName,
        roster: [],
        remarks: '',
      });
      return newSchedule;
    });
  };

  return (
    <div className="bg-slate-50 min-h-screen">
      <div
        className={`fixed top-5 right-5 z-[100] transition-all duration-300 ${saveStatus.visible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-5 pointer-events-none'}`}
      >
        <div
          className={`px-4 py-2 rounded-lg shadow-lg text-white font-semibold ${saveStatus.isError ? 'bg-red-600' : 'bg-slate-800'}`}
        >
          {saveStatus.msg}
        </div>
      </div>

      <div className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto bg-white rounded-xl shadow-lg p-5 sm:p-8 relative">
          {isLoading && <LoadingOverlay text={loadingText} />}
          <header className="flex flex-wrap items-center justify-between mb-6 gap-4">
            <div className="flex items-center gap-4 flex-wrap">
              <h1 className="text-2xl font-bold text-slate-800">人力需求規則設定</h1>
              <select
                value={currentUnitId}
                onChange={(e) => setCurrentUnitId(e.target.value)}
                className="form-select"
              >
                {units.map((unit) => (
                  <option key={unit.id} value={unit.id}>
                    {unit.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={loadData}
                className="px-4 py-2 bg-sky-600 text-white font-semibold rounded-lg shadow-md hover:bg-sky-700"
              >
                讀取設定
              </button>
              <button
                onClick={handleSaveSettings}
                className="px-4 py-2 bg-orange-600 text-white font-semibold rounded-lg shadow-md hover:bg-orange-700"
              >
                儲存設定
              </button>
            </div>
          </header>

          {scheduleWithRosterText ? (
            <ScheduleTable
              schedule={scheduleWithRosterText}
              onUpdate={handleScheduleUpdate}
              onShowRosterModal={(role) => {
                setEditingRole(role);
                setIsRosterModalOpen(true);
              }}
              onShowActionModal={showActionModal}
            />
          ) : (
            <div className="text-center py-16 text-slate-500">
              正在載入設定，或點擊「讀取設定」開始。
            </div>
          )}

          <div className="mt-4 flex flex-col sm:flex-row gap-4">
            <button
              onClick={handleAddRole}
              className="px-4 py-2 bg-slate-100 text-slate-800 font-medium rounded-md hover:bg-slate-200"
              disabled={!currentSchedule}
            >
              + 新增群組
            </button>
            <button
              onClick={() => showActionModal({ type: 'add-timeslot' })}
              className="px-4 py-2 bg-slate-100 text-slate-800 font-medium rounded-md hover:bg-slate-200"
              disabled={!currentSchedule}
            >
              + 新增時間段
            </button>
          </div>
        </div>
      </div>

      <RosterModal
        isOpen={isRosterModalOpen}
        onClose={() => setIsRosterModalOpen(false)}
        role={editingRole}
        unitId={currentUnitId}
        staffData={staffData}
        staffAssignments={staffAssignments}
        onSave={(roleId, roster) => handleScheduleUpdate(roleId, 'roster', roster)}
      />

      <ActionModal
        config={actionModalConfig}
        onConfirm={handleActionConfirm}
        onClose={closeModal}
      />
    </div>
  );
}
