import { useEffect, useCallback } from 'react';
import { useMessageCenter, useParentData } from './ParentData';
import { POST_MESSAGE_TYPES } from '@/lib/constants';

/**
 * MessageCenter 使用範例
 * 展示如何使用新的統一 API 以及向後兼容的 API
 */
export function MessageCenterExample() {
  // === 新的統一 API ===
  const messageCenter = useMessageCenter();

  // === 向後兼容的 API (保持不變) ===
  const parentData = useParentData();

  // 使用新的統一 API 發送消息
  const handleSendHealthCheck = useCallback(async () => {
    try {
      const isHealthy = await messageCenter.healthCheck();
      console.log('健康檢查結果:', isHealthy);
    } catch (error) {
      console.error('健康檢查失敗:', error);
    }
  }, [messageCenter]);

  // 使用新的統一 API 註冊自定義消息處理器
  useEffect(() => {
    const unregister = messageCenter.registerHandler(
      POST_MESSAGE_TYPES.HEALTH_CHECK_RESPONSE,
      (data: any) => {
        console.log('收到健康檢查回應:', data);
      }
    );

    return unregister;
  }, [messageCenter]);

  // 使用新的統一 API 發送自定義消息
  const handleSendCustomMessage = useCallback(async () => {
    try {
      await messageCenter.sendMessage(POST_MESSAGE_TYPES.SCHEDULE_REFRESH, {
        action: 'refresh',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('發送自定義消息失敗:', error);
    }
  }, [messageCenter]);

  // 使用向後兼容的 API (現有代碼無需修改)
  const handleRequestProcessedData = useCallback(() => {
    parentData.requestProcessedData([]);
  }, [parentData]);

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-lg font-semibold">MessageCenter API 使用範例</h2>

      {/* 嵌入環境檢測 */}
      <div className="bg-blue-50 p-3 rounded">
        <p>
          <strong>嵌入環境檢測:</strong> {messageCenter.isEmbedded ? '✅ 已嵌入' : '❌ 未嵌入'}
        </p>
      </div>

      {/* 錯誤狀態 */}
      {messageCenter.lastError && (
        <div className="bg-red-50 p-3 rounded">
          <p>
            <strong>最後錯誤:</strong> {messageCenter.lastError.message}
          </p>
          <p>
            <strong>時間:</strong> {messageCenter.lastError.timestamp}
          </p>
        </div>
      )}

      {/* 向後兼容的 API */}
      <div className="bg-green-50 p-3 rounded">
        <h3 className="font-semibold">向後兼容 API (現有代碼無需修改)</h3>
        <p>載入中: {parentData.isLoading ? '是' : '否'}</p>
        <p>處理後資料: {parentData.processedData ? '有' : '無'}</p>
        <button
          onClick={handleRequestProcessedData}
          className="mt-2 px-4 py-2 bg-green-500 text-white rounded"
        >
          請求處理後資料 (舊 API)
        </button>
      </div>

      {/* 新的統一 API */}
      <div className="bg-purple-50 p-3 rounded">
        <h3 className="font-semibold">新的統一 API</h3>
        <div className="space-x-2 mt-2">
          <button
            onClick={handleSendHealthCheck}
            className="px-4 py-2 bg-purple-500 text-white rounded"
          >
            健康檢查
          </button>
          <button
            onClick={handleSendCustomMessage}
            className="px-4 py-2 bg-purple-500 text-white rounded"
          >
            發送自定義消息
          </button>
        </div>
      </div>

      {/* 使用說明 */}
      <div className="bg-gray-50 p-3 rounded">
        <h3 className="font-semibold">遷移指南</h3>
        <ul className="list-disc pl-5 mt-2 space-y-1 text-sm">
          <li>
            <strong>現有代碼:</strong> 使用 <code>useParentData()</code> 的代碼無需修改
          </li>
          <li>
            <strong>新功能:</strong> 使用 <code>useMessageCenter()</code> 獲取統一的訊息中心功能
          </li>
          <li>
            <strong>消息發送:</strong> 使用 <code>messageCenter.sendMessage()</code>{' '}
            發送統一格式的消息
          </li>
          <li>
            <strong>事件監聽:</strong> 使用 <code>messageCenter.registerHandler()</code>{' '}
            註冊事件處理器
          </li>
          <li>
            <strong>嵌入檢測:</strong> 使用 <code>messageCenter.isEmbedded</code> 檢測是否在嵌入環境
          </li>
        </ul>
      </div>
    </div>
  );
}

/**
 * 新的統一 API 使用範例 Hook
 * 展示如何在自定義 Hook 中使用新的 MessageCenter API
 */
export function useCustomMessageHandler() {
  const { sendMessage, registerHandler, isEmbedded } = useMessageCenter();

  // 註冊自定義消息處理器
  useEffect(() => {
    if (!isEmbedded) return;

    const unregisterScheduleRefresh = registerHandler(
      POST_MESSAGE_TYPES.SCHEDULE_REFRESH,
      (data: any) => {
        console.log('收到排班刷新消息:', data);
        // 處理排班刷新邏輯
      }
    );

    const unregisterHealthCheck = registerHandler(POST_MESSAGE_TYPES.HEALTH_CHECK, (data: any) => {
      console.log('收到健康檢查請求:', data);
      // 回應健康檢查
      sendMessage(POST_MESSAGE_TYPES.HEALTH_CHECK_RESPONSE, {
        status: 'healthy',
        timestamp: new Date().toISOString(),
      });
    });

    return () => {
      unregisterScheduleRefresh();
      unregisterHealthCheck();
    };
  }, [sendMessage, registerHandler, isEmbedded]);

  // 提供給組件使用的方法
  const refreshSchedule = useCallback(async () => {
    if (!isEmbedded) {
      console.warn('不在嵌入環境中，無法刷新排班');
      return;
    }

    try {
      await sendMessage(POST_MESSAGE_TYPES.SCHEDULE_REFRESH, {
        action: 'refresh',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('刷新排班失敗:', error);
    }
  }, [sendMessage, isEmbedded]);

  return {
    refreshSchedule,
    isEmbedded,
  };
}
