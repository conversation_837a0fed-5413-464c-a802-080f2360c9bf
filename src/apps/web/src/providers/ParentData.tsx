import {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
  ReactNode,
  useRef,
} from 'react';
import { Message } from '@langchain/langgraph-sdk';
import { ShiftDataResponse } from '@/datas/api.types';
import {
  sendMessage as postRobotSendMessage,
  setupListener as postRobotSetupListener,
} from '@/lib/postRobot';
import {
  POST_MESSAGE_TYPES,
  PostMessageType,
  PostMessageHandler,
  PostMessageError,
} from '@/lib/constants';

// 嵌入環境檢測
const isEmbedded = () => {
  try {
    return window.parent !== window && window.parent !== null;
  } catch (error) {
    // 如果無法訪問 window.parent，通常表示跨域限制
    return false;
  }
};

// 統一的消息中心 Context 類型
interface MessageCenterContextType {
  // === 向後兼容的 API ===
  requestProcessedData: (messages: Message[]) => void;
  processedData: ShiftDataResponse | null;
  isLoading: boolean;

  // === 新的統一 API ===
  // 嵌入環境檢測
  isEmbedded: boolean;

  // 統一消息發送
  sendMessage: <T = any>(type: PostMessageType, payload: T, targetWindow?: Window) => Promise<void>;

  // 統一事件監聽註冊
  registerHandler: <T = any>(type: PostMessageType, handler: PostMessageHandler<T>) => () => void;

  // 錯誤處理
  lastError: PostMessageError | null;

  // 健康檢查
  healthCheck: () => Promise<boolean>;
}

const MessageCenterContext = createContext<MessageCenterContextType>({
  // 向後兼容的預設值
  requestProcessedData: () => {},
  processedData: null,
  isLoading: false,

  // 新的統一 API 預設值
  isEmbedded: false,
  sendMessage: async () => {},
  registerHandler: () => () => {},
  lastError: null,
  healthCheck: async () => false,
});

// 向後兼容的 Hook
export function useParentData() {
  const context = useContext(MessageCenterContext);
  return {
    requestProcessedData: context.requestProcessedData,
    processedData: context.processedData,
    isLoading: context.isLoading,
  };
}

// 新的統一 Hook
export function useMessageCenter() {
  return useContext(MessageCenterContext);
}

interface MessageCenterProviderProps {
  children: ReactNode;
}

/**
 * 統一的 postMessage 訊息中心 Provider
 * 提供統一的跨窗口通信管理機制
 * 同時保持向後兼容的 API
 */
export function MessageCenterProvider({ children }: MessageCenterProviderProps) {
  // === 向後兼容的狀態 ===
  const [processedData, setProcessedData] = useState<ShiftDataResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // === 新的統一狀態 ===
  const [lastError, setLastError] = useState<PostMessageError | null>(null);
  const handlersRef = useRef<Map<PostMessageType, PostMessageHandler[]>>(new Map());
  const listenersRef = useRef<Map<PostMessageType, any>>(new Map());
  const embeddedStatus = isEmbedded();

  // 清理錯誤狀態
  const clearError = useCallback(() => {
    setLastError(null);
  }, []);

  // 統一的消息發送方法
  const sendMessage = useCallback(
    async <T = any,>(
      type: PostMessageType,
      payload: T,
      targetWindow: Window = window.parent
    ): Promise<void> => {
      if (!embeddedStatus) {
        console.warn('Not in embedded environment, message not sent:', type);
        return;
      }

      try {
        clearError();
        await postRobotSendMessage(targetWindow, type, {
          type,
          payload,
          timestamp: new Date().toISOString(),
          source: 'agent-chat-app',
        });

        console.log(`Message sent successfully: ${type}`, payload);
      } catch (error) {
        const messageError: PostMessageError = {
          type,
          message: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
          originalError: error instanceof Error ? error : undefined,
        };

        setLastError(messageError);
        console.error('Error sending message:', messageError);
        throw error;
      }
    },
    [embeddedStatus, clearError]
  );

  // 統一的事件監聽註冊
  const registerHandler = useCallback(
    <T = any,>(type: PostMessageType, handler: PostMessageHandler<T>): (() => void) => {
      // 註冊處理器
      const currentHandlers = handlersRef.current.get(type) || [];
      currentHandlers.push(handler);
      handlersRef.current.set(type, currentHandlers);

      // 如果是第一個處理器，設置監聽器
      if (currentHandlers.length === 1) {
        const listener = postRobotSetupListener(type, async (data: T) => {
          const handlers = handlersRef.current.get(type) || [];

          // 並行執行所有處理器
          await Promise.all(
            handlers.map(async (h) => {
              try {
                await h(data);
              } catch (error) {
                const messageError: PostMessageError = {
                  type,
                  message: error instanceof Error ? error.message : 'Handler error',
                  timestamp: new Date().toISOString(),
                  originalError: error instanceof Error ? error : undefined,
                };

                setLastError(messageError);
                console.error(`Error in handler for ${type}:`, messageError);
              }
            })
          );
        });

        listenersRef.current.set(type, listener);
      }

      // 返回取消註冊函數
      return () => {
        const currentHandlers = handlersRef.current.get(type) || [];
        const updatedHandlers = currentHandlers.filter((h) => h !== handler);

        if (updatedHandlers.length === 0) {
          // 如果沒有處理器了，取消監聽器
          const listener = listenersRef.current.get(type);
          if (listener && typeof listener.cancel === 'function') {
            listener.cancel();
          }
          listenersRef.current.delete(type);
          handlersRef.current.delete(type);
        } else {
          handlersRef.current.set(type, updatedHandlers);
        }
      };
    },
    []
  );

  // 健康檢查方法
  const healthCheck = useCallback(async (): Promise<boolean> => {
    if (!embeddedStatus) {
      return false;
    }

    try {
      await sendMessage(POST_MESSAGE_TYPES.HEALTH_CHECK, {
        timestamp: new Date().toISOString(),
        source: 'agent-chat-app',
      });
      return true;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }, [embeddedStatus, sendMessage]);

  // === 向後兼容的方法 ===
  const requestProcessedData = useCallback(
    (messages: Message[]) => {
      console.log('發送父窗口數據請求...');
      setIsLoading(true);

      sendMessage(POST_MESSAGE_TYPES.REQUEST_PROCESSED_DATA, {
        messages,
        timestamp: new Date().toISOString(),
      }).catch(() => {
        setIsLoading(false);
      });
    },
    [sendMessage]
  );

  // === 向後兼容的監聽器設置 ===
  useEffect(() => {
    console.log('設置父窗口響應監聽器...');

    const unregister = registerHandler(
      POST_MESSAGE_TYPES.PROCESSED_DATA_RESPONSE,
      (data: ShiftDataResponse) => {
        console.log('收到父窗口處理後的資料:', data);
        setIsLoading(false);
        setProcessedData(data);
      }
    );

    return unregister;
  }, [registerHandler]);

  // 清理所有監聽器
  useEffect(() => {
    return () => {
      // 清理所有監聽器
      listenersRef.current.forEach((listener) => {
        if (listener && typeof listener.cancel === 'function') {
          listener.cancel();
        }
      });
      listenersRef.current.clear();
      handlersRef.current.clear();
    };
  }, []);

  const value: MessageCenterContextType = {
    // === 向後兼容的 API ===
    requestProcessedData,
    processedData,
    isLoading,

    // === 新的統一 API ===
    isEmbedded: embeddedStatus,
    sendMessage,
    registerHandler,
    lastError,
    healthCheck,
  };

  return <MessageCenterContext.Provider value={value}>{children}</MessageCenterContext.Provider>;
}

// 向後兼容的 Provider 別名
export const ParentDataProvider = MessageCenterProvider;
