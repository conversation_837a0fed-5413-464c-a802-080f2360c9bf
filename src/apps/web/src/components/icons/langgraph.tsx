export function LangGraphLogoSVG({
  className,
  width,
  height,
}: {
  width?: number;
  height?: number;
  className?: string;
}) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 98 51"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M25.5144 0.394531H73.3011C86.9178 0.394531 97.9949 11.5154 97.9949 25.1847C97.9949 38.8539 86.9178 49.9748 73.3011 49.9748H25.5144C11.8977 49.9748 0.820557 38.8539 0.820557 25.1847C0.820557 11.5154 11.8977 0.394531 25.5144 0.394531ZM47.4544 38.8228C48.0543 39.454 48.9423 39.4228 49.7291 39.2592L49.7369 39.2631C50.1021 38.9659 49.583 38.5896 49.0873 38.2302C48.79 38.0146 48.5012 37.8052 48.4165 37.6226C48.6908 37.2878 47.8793 36.5277 47.2475 35.936C46.9822 35.6875 46.7487 35.4688 46.6404 35.3236C46.1908 34.8341 46.0101 34.2163 45.8283 33.5949C45.7077 33.1826 45.5866 32.7687 45.3862 32.3895C44.1516 29.5216 42.7377 26.6771 40.7552 24.2495C39.4811 22.636 38.027 21.1911 36.5723 19.7457C35.6346 18.8139 34.6967 17.8819 33.8066 16.9044C32.8908 15.9585 32.3396 14.7932 31.7874 13.6259C31.3252 12.6488 30.8624 11.6702 30.1844 10.8179C28.1317 7.77859 21.6506 6.94861 20.7002 11.2427C20.7041 11.3751 20.6613 11.4609 20.5444 11.5466C20.0186 11.9324 19.5512 12.3688 19.1578 12.8987C18.1958 14.243 18.0478 16.5225 19.2474 17.7305C19.2492 17.7046 19.2508 17.6787 19.2525 17.653C19.2926 17.043 19.3301 16.4729 19.8122 16.0355C20.7392 16.8343 22.1452 17.1187 23.2202 16.5225C24.5164 18.3826 24.9292 20.6311 25.3435 22.8873C25.6886 24.7667 26.0347 26.6515 26.8931 28.3214C26.9109 28.351 26.9286 28.3805 26.9464 28.4101C27.451 29.2506 27.9637 30.1046 28.6108 30.8386C28.8459 31.2032 29.3286 31.5967 29.8104 31.9895C30.4462 32.5079 31.0805 33.025 31.1425 33.4727C31.1453 33.6676 31.1445 33.865 31.1436 34.0636C31.1386 35.2395 31.1334 36.4572 31.8864 37.4239C32.3032 38.2695 31.2827 39.1189 30.4609 39.0137C30.0103 39.0762 29.518 38.9574 29.0292 38.8395C28.3604 38.6781 27.6981 38.5182 27.158 38.8267C27.0065 38.9907 26.7889 38.9965 26.5702 39.0022C26.311 39.0091 26.0503 39.016 25.896 39.2865C25.8644 39.3669 25.7903 39.4577 25.7133 39.5521C25.5443 39.7594 25.3611 39.9841 25.5806 40.1554C25.6002 40.1405 25.6198 40.1255 25.6393 40.1106C25.9718 39.8568 26.2885 39.6149 26.7374 39.7658C26.6777 40.0975 26.8918 40.1863 27.1058 40.2751C27.1432 40.2906 27.1805 40.3062 27.2164 40.323C27.2141 40.4 27.199 40.4777 27.1839 40.5548C27.1479 40.739 27.1126 40.9196 27.2554 41.0789C27.3232 41.0099 27.3831 40.9325 27.4431 40.8548C27.5901 40.6649 27.7378 40.4739 28.0032 40.4048C28.5871 41.1849 29.1753 40.8609 29.9134 40.4543C30.7458 39.9958 31.7688 39.4322 33.1912 40.2294C32.646 40.2022 32.1591 40.2684 31.793 40.7204C31.7034 40.8217 31.6255 40.9386 31.7852 41.0711C32.6268 40.5256 32.9769 40.7217 33.3065 40.9062C33.5444 41.0394 33.7715 41.1666 34.165 41.0049C34.258 40.9563 34.351 40.9062 34.4441 40.8559C35.0759 40.5149 35.7167 40.169 36.4669 40.2879C35.9065 40.4496 35.7072 40.8049 35.4896 41.1928C35.382 41.3845 35.2699 41.5843 35.1075 41.7725C35.0219 41.8582 34.9829 41.9595 35.0803 42.1037C36.2536 42.0061 36.6969 41.7085 37.2959 41.3064C37.5817 41.1145 37.903 40.8989 38.3559 40.6698C38.8566 40.3614 39.3573 40.5586 39.8425 40.7498C40.3689 40.9571 40.877 41.1573 41.3472 40.697C41.4957 40.557 41.6819 40.5553 41.8675 40.5536C41.9349 40.553 42.0023 40.5523 42.0678 40.5451C41.9215 39.7609 41.0961 39.7702 40.2582 39.7795C39.2891 39.7903 38.3033 39.8014 38.3325 38.5851C39.233 37.9699 39.2413 36.9021 39.2492 35.8929C39.2511 35.6493 39.2529 35.4091 39.2673 35.1795C39.9296 35.5489 40.6302 35.8376 41.3264 36.1246C41.9813 36.3945 42.6323 36.6628 43.244 36.9953C43.8828 38.024 44.8799 39.3878 46.2081 39.2982C46.2431 39.193 46.2743 39.1033 46.3132 38.9981C46.3898 39.0115 46.4706 39.032 46.5529 39.0528C46.9014 39.1412 47.2748 39.2358 47.4544 38.8228ZM73.48 27.1315C74.249 27.899 75.2921 28.3302 76.3797 28.3302C77.4673 28.3302 78.5103 27.899 79.2794 27.1315C80.0484 26.364 80.4804 25.323 80.4804 24.2375C80.4804 23.1521 80.0484 22.1111 79.2794 21.3436C78.5103 20.5761 77.4673 20.1449 76.3797 20.1449C75.871 20.1449 75.3721 20.2392 74.9064 20.4181L72.5533 16.9819L70.9152 18.1046L73.28 21.558C72.6365 22.2995 72.2789 23.2501 72.2789 24.2375C72.2789 25.323 72.711 26.364 73.48 27.1315ZM66.1213 16.0159C66.6967 16.3004 67.331 16.446 67.9731 16.441C68.8492 16.4343 69.7002 16.1477 70.4012 15.6232C71.1022 15.0987 71.6165 14.3639 71.8687 13.5265C72.1209 12.6891 72.0977 11.7931 71.8025 10.9698C71.5074 10.1465 70.9558 9.43917 70.2285 8.95149C69.6956 8.59407 69.0859 8.36657 68.4487 8.28729C67.8115 8.20802 67.1646 8.27919 66.56 8.49509C65.9554 8.71098 65.4101 9.06555 64.9679 9.53025C64.5257 9.99495 64.1991 10.5568 64.0142 11.1705C63.8294 11.7843 63.7916 12.4327 63.9038 13.0637C64.016 13.6947 64.2751 14.2906 64.6603 14.8034C65.0455 15.3161 65.5459 15.7315 66.1213 16.0159ZM66.1213 39.7813C66.6967 40.0657 67.331 40.2113 67.9731 40.2064C68.8492 40.1996 69.7002 39.913 70.4012 39.3885C71.1022 38.864 71.6165 38.1292 71.8687 37.2918C72.1209 36.4544 72.0977 35.5584 71.8025 34.7351C71.5074 33.9118 70.9558 33.2045 70.2285 32.7168C69.6956 32.3594 69.0859 32.1319 68.4487 32.0526C67.8115 31.9734 67.1646 32.0445 66.56 32.2604C65.9554 32.4763 65.4101 32.8309 64.9679 33.2956C64.5257 33.7603 64.1991 34.3221 64.0142 34.9359C63.8294 35.5496 63.7916 36.1981 63.9038 36.8291C64.016 37.4601 64.2751 38.0559 64.6603 38.5687C65.0455 39.0815 65.5459 39.4968 66.1213 39.7813ZM69.8934 25.2555V23.2207H63.6171C63.4592 22.6038 63.1581 22.0323 62.738 21.5523L65.0993 18.0525L63.382 16.9131L61.0207 20.4128C60.5879 20.2564 60.1317 20.1738 59.6714 20.1686C58.5869 20.1686 57.5469 20.5974 56.7801 21.3606C56.0133 22.1237 55.5825 23.1588 55.5825 24.2381C55.5825 25.3174 56.0133 26.3525 56.7801 27.1156C57.5469 27.8788 58.5869 28.3076 59.6714 28.3076C60.1317 28.3024 60.5879 28.2198 61.0207 28.0634L63.382 31.5631L65.0788 30.4237L62.738 26.9239C63.1581 26.4439 63.4592 25.8724 63.6171 25.2555H69.8934Z"
        fill="#264849"
      />
    </svg>
  );
}
