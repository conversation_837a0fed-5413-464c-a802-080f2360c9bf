import { parsePartialJson } from '@langchain/core/output_parsers';
import { useStreamContext } from '@/providers/Stream';
import { AIMessage, Checkpoint, Message } from '@langchain/langgraph-sdk';
import { getContentString } from '../utils';
import { BranchSwitcher, CommandBar } from './shared';
import { MarkdownText } from '../markdown-text';
import { LoadExternalComponent } from '@langchain/langgraph-sdk/react-ui';
import { cn } from '@/lib/utils';
import { ToolCalls, ToolResult } from './tool-calls';
import { MessageContentComplex } from '@langchain/core/messages';
import { Fragment } from 'react/jsx-runtime';
import { isAgentInboxInterruptSchema } from '@/lib/agent-inbox-interrupt';
import { ThreadView } from '../agent-inbox';
import { useQueryState, parseAsBoolean } from 'nuqs';
import { GenericInterruptView } from './generic-interrupt';
import { But<PERSON> } from '@/components/ui/button';
import { Download, LoaderCircle } from 'lucide-react';
import { CommandPayload, CommandType } from '@/lib/constants';

function CustomComponent({
  message,
  thread,
}: {
  message: Message;
  thread: ReturnType<typeof useStreamContext>;
}) {
  const { values } = useStreamContext();
  const customComponents = values.ui?.filter((ui) => ui.metadata?.message_id === message.id);

  if (!customComponents?.length) return null;
  return (
    <Fragment key={message.id}>
      {customComponents.map((customComponent) => (
        <LoadExternalComponent
          key={customComponent.id}
          stream={thread}
          message={customComponent}
          meta={{ ui: customComponent }}
        />
      ))}
    </Fragment>
  );
}

function parseAnthropicStreamedToolCalls(
  content: MessageContentComplex[]
): AIMessage['tool_calls'] {
  const toolCallContents = content.filter((c) => c.type === 'tool_use' && c.id);

  return toolCallContents.map((tc) => {
    const toolCall = tc as Record<string, any>;
    let json: Record<string, any> = {};
    if (toolCall?.input) {
      try {
        json = parsePartialJson(toolCall.input) ?? {};
      } catch {
        // Pass
      }
    }
    return {
      name: toolCall.name ?? '',
      id: toolCall.id ?? '',
      args: json,
      type: 'tool_call',
    };
  });
}

export function AssistantMessage({
  message,
  isLoading,
  handleRegenerate,
  isWaitingForChildData,
  onDownload,
}: {
  message: (Message & { command?: CommandPayload }) | undefined;
  isLoading: boolean;
  handleRegenerate: (parentCheckpoint: Checkpoint | null | undefined) => void;
  isWaitingForChildData: boolean;
  onDownload: () => void;
}) {
  const content = message?.content ?? [];
  const contentString = getContentString(content);
  const [hideToolCalls] = useQueryState('hideToolCalls', parseAsBoolean.withDefault(false));

  const thread = useStreamContext();
  const isLastMessage = thread.messages[thread.messages.length - 1].id === message?.id;
  const isDownloadShiftSchedule = message?.command?.type === CommandType.REFRESH && isLastMessage;
  const hasNoAIOrToolMessages = !thread.messages.find((m) => m.type === 'ai' || m.type === 'tool');
  const meta = message ? thread.getMessagesMetadata(message) : undefined;
  const threadInterrupt = thread.interrupt;

  const parentCheckpoint = meta?.firstSeenState?.parent_checkpoint;
  const anthropicStreamedToolCalls = Array.isArray(content)
    ? parseAnthropicStreamedToolCalls(content)
    : undefined;

  const hasToolCalls =
    message && 'tool_calls' in message && message.tool_calls && message.tool_calls.length > 0;
  const toolCallsHaveContents =
    hasToolCalls && message.tool_calls?.some((tc) => tc.args && Object.keys(tc.args).length > 0);
  const hasAnthropicToolCalls = !!anthropicStreamedToolCalls?.length;
  const isToolResult = message?.type === 'tool';

  if (isToolResult && hideToolCalls) {
    return null;
  }

  return (
    <div className="flex items-start mr-auto gap-2 group rounded-2xl bg-blue/20 p-4 text-black-800 w-full max-w-full">
      {isToolResult ? (
        <ToolResult message={message} />
      ) : (
        <div className="flex flex-col gap-2 w-full max-w-full">
          {contentString.length > 0 && (
            <div className="py-1 break-words">
              <MarkdownText>{contentString}</MarkdownText>
            </div>
          )}

          {!hideToolCalls && (
            <>
              {(hasToolCalls && toolCallsHaveContents && (
                <ToolCalls toolCalls={message.tool_calls} />
              )) ||
                (hasAnthropicToolCalls && <ToolCalls toolCalls={anthropicStreamedToolCalls} />) ||
                (hasToolCalls && <ToolCalls toolCalls={message.tool_calls} />)}
            </>
          )}

          {message && <CustomComponent message={message} thread={thread} />}
          {isAgentInboxInterruptSchema(threadInterrupt?.value) &&
            (isLastMessage || hasNoAIOrToolMessages) && (
              <ThreadView interrupt={threadInterrupt.value} />
            )}
          {threadInterrupt?.value &&
          !isAgentInboxInterruptSchema(threadInterrupt.value) &&
          isLastMessage ? (
            <GenericInterruptView interrupt={threadInterrupt.value} />
          ) : null}
          <div
            className={cn(
              'flex gap-2 items-center mr-auto transition-opacity',
              'opacity-0 group-focus-within:opacity-100 group-hover:opacity-100'
            )}
          >
            <BranchSwitcher
              branch={meta?.branch}
              branchOptions={meta?.branchOptions}
              onSelect={(branch) => thread.setBranch(branch)}
              isLoading={isLoading}
            />
            <CommandBar
              content={contentString}
              isLoading={isLoading}
              isAiMessage={true}
              handleRegenerate={() => handleRegenerate(parentCheckpoint)}
            />
            {isDownloadShiftSchedule && (
              <Button
                variant="outline"
                className="rounded-full text-green-600 hover:text-white bg-green-100 hover:bg-green-600 border-green-300"
                onClick={onDownload}
                disabled={isLoading || isWaitingForChildData}
              >
                {isWaitingForChildData ? (
                  <>
                    <LoaderCircle className="w-4 h-4 mr-1 animate-spin" />
                    處理中...
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4 mr-1" />
                    下載當月班表
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export function AssistantMessageLoading() {
  return (
    <div className="flex items-start mr-auto gap-2 p-4 text-blue w-fit">
      <div className="flex items-center gap-3">
        <LoaderCircle className="w-5 h-5 animate-spin text-blue" />
        <span className="text-blue text-sm font-medium">思考中...</span>
      </div>
    </div>
  );
}
