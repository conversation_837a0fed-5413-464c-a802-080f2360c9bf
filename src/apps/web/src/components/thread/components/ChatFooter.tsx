import { FormEvent } from 'react';
import { Button } from '../../ui/button';
import { LoaderCircle, Send } from 'lucide-react';
import { QuickActionButtons } from './QuickActionButtons';

interface ChatFooterProps {
  input: string;
  setInput: (value: string) => void;
  isLoading: boolean;
  onSubmit: (e: FormEvent) => void;
  onSubmitContent: (content: string) => () => void;
  onStop: () => void;
}

/**
 * 聊天底部組件
 * 包含快速操作按鈕、輸入框和提交按鈕
 */
export function ChatFooter({
  input,
  setInput,
  isLoading,
  onSubmit,
  onSubmitContent,
  onStop,
}: ChatFooterProps) {
  return (
    <div className="sticky flex flex-col items-center gap-2 bottom-0 bg-white">
      <QuickActionButtons isLoading={isLoading} onSubmitContent={onSubmitContent} />

      <div className="bg-[#F1F1F1] border shadow-xs mx-auto w-full max-w-3xl relative z-10">
        <form
          onSubmit={onSubmit}
          className="gap-2 max-w-3xl mx-auto flex items-center justify-between"
        >
          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey && !e.metaKey && !e.nativeEvent.isComposing) {
                e.preventDefault();
                const el = e.target as HTMLElement | undefined;
                const form = el?.closest('form');
                form?.requestSubmit();
              }
            }}
            placeholder="點擊上方建議或直接輸入"
            className="outline-none focus:outline-none focus:ring-0 resize-none bg-white m-2 rounded-md flex-4/5 py-2 px-4"
          />

          <div className="flex-1/5">
            {isLoading ? (
              <Button key="stop" onClick={onStop}>
                <LoaderCircle className="w-4 h-4 animate-spin" />
              </Button>
            ) : (
              <Button
                type="submit"
                className="transition-all bg-transparent"
                disabled={isLoading || !input.trim()}
              >
                <Send className="size-6 text-blue" />
              </Button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}
