import { motion } from 'framer-motion';
import AILogoSVG from '../../icons/ai_icon.svg?react';

interface XELogoButtonProps {
  onClick: () => void;
  marginLeft?: number;
}

/**
 * XE AI Logo 按鈕組件
 * 顯示AI助手的標識和在線狀態
 */
export function XELogoButton({ onClick, marginLeft = 0 }: XELogoButtonProps) {
  return (
    <motion.button
      className="flex gap-2 items-center cursor-pointer min-w-32"
      onClick={onClick}
      animate={{
        marginLeft,
      }}
      transition={{
        type: 'spring',
        stiffness: 300,
        damping: 30,
      }}
    >
      <AILogoSVG className="w-10 h-10" />
      <div className="flex flex-col items-start">
        <span className="text-sm font-bold text-gray-800">MAYO AI</span>
        <span className="text-xs text-blue-400">online now</span>
      </div>
    </motion.button>
  );
}
