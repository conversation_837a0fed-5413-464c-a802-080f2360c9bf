import { ReactNode } from "react";
import { useStickToBottomContext } from "use-stick-to-bottom";

interface StickyToBottomContentProps {
  content: ReactNode;
  footer?: ReactNode;
  className?: string;
  contentClassName?: string;
}

/**
 * 固定到底部內容組件
 * 處理聊天內容的滾動和布局，確保新內容出現時自動滾動到底部
 */
export function StickyToBottomContent({
  content,
  footer,
  className,
  contentClassName
}: StickyToBottomContentProps) {
  const context = useStickToBottomContext();
  
  return (
    <div
      ref={context.scrollRef}
      style={{ width: "100%", height: "100%" }}
      className={className}
    >
      <div ref={context.contentRef} className={contentClassName}>
        {content}
      </div>
      {footer}
    </div>
  );
} 