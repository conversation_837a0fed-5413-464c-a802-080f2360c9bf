import { Button } from '../../ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { AlertCircle, ChevronLeft, ChevronRight } from 'lucide-react';
import { useState } from 'react';

interface QuickActionButtonsProps {
  isLoading: boolean;
  onSubmitContent: (content: string) => () => void;
}

const guides = [
  {
    title: '最低成本',
    text: '1：優先排滿月薪人員​。\n2：人力從時薪制低時薪者開始補齊​。',
  },
  {
    title: '平均工時',
    text: '1：區分薪制​。\n2：拉進同薪制人員工時​。',
  },
  {
    title: '平均時段',
    text: '1：確保每人輪替各種班次​。\n2：適用人員設定班別的所有班次​​。',
  },
];

/**
 * 快速操作按鈕組件
 * 包含預設的聊天選項和下載功能
 */
export function QuickActionButtons({ isLoading, onSubmitContent }: QuickActionButtonsProps) {
  const [index, setIndex] = useState(0);
  const { title, text } = guides[index];
  return (
    <div className="flex w-full items-center justify-center gap-1 flex-wrap ">
      <Button
        variant="outline"
        className="rounded-full flex-1 min-w-0 text-blue hover:text-white bg-blue/20 hover:bg-blue/80 ml-1"
        onClick={onSubmitContent('採用最低成本策略排班')}
        disabled={isLoading}
      >
        最低成本
      </Button>

      <Button
        variant="outline"
        className="rounded-full flex-1 min-w-0 text-blue hover:text-white bg-blue/20 hover:bg-blue/80"
        onClick={onSubmitContent('採用平均工時策略排班')}
        disabled={isLoading}
      >
        平均工時
      </Button>

      <Button
        variant="outline"
        className="rounded-full flex-1 min-w-0 text-blue hover:text-white bg-blue/20 hover:bg-blue/80"
        onClick={onSubmitContent('採用平均時段策略排班')}
        disabled={isLoading}
      >
        平均時段
      </Button>

      {/* 驚嘆號 Popover 說明 */}
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="flex-1 min-w-0 text-blue/80 hover:bg-blue/10"
            aria-label="快速功能說明"
          >
            <AlertCircle className="h-5 w-5" />
          </Button>
        </PopoverTrigger>

        <PopoverContent side="top" align="end" sideOffset={10} className="bg-[#E2F0F7]">
          <div className="space-y-2">
            <h4 className="font-medium text-blue">{title}</h4>
            <p className="whitespace-pre-line leading-relaxed">{text}</p>
          </div>

          <div className="mt-4 flex items-center justify-between">
            <Button
              variant="ghost"
              size="icon"
              disabled={index === 0}
              onClick={() => setIndex((i) => Math.max(i - 1, 0))}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-xs text-muted-foreground">
              {index + 1}/{guides.length}
            </span>
            <Button
              variant="ghost"
              size="icon"
              disabled={index === guides.length - 1}
              onClick={() => setIndex((i) => Math.min(i + 1, guides.length - 1))}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
