import { ArrowDown } from "lucide-react";
import { But<PERSON> } from "../../ui/button";
import { useStickToBottomContext } from "use-stick-to-bottom";

interface ScrollToBottomProps {
  className?: string;
}

/**
 * 滾動到底部按鈕組件
 * 當用戶不在底部時顯示，點擊可滾動到最底部
 */
export function ScrollToBottom({ className }: ScrollToBottomProps) {
  const { isAtBottom, scrollToBottom } = useStickToBottomContext();

  if (isAtBottom) return null;
  
  return (
    <Button
      variant="outline"
      className={className}
      onClick={() => scrollToBottom()}
    >
      <ArrowDown className="w-4 h-4" />
      <span>Scroll to bottom</span>
    </Button>
  );
} 