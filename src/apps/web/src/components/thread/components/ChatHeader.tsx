import { TooltipIconButton } from '../tooltip-icon-button';
import { XELogoButton } from './XELogoButton';
import { Settings } from 'lucide-react';

interface ChatHeaderProps {
  chatStarted?: boolean;
  onNewThread: () => void;
  onOpenShiftSettings: () => void;
}

/**
 * 聊天頭部組件
 * 包含導航按鈕、Logo和設置選項
 */
export function ChatHeader({ chatStarted, onNewThread, onOpenShiftSettings }: ChatHeaderProps) {
  console.log('chatStarted', chatStarted);
  return (
    <div className="flex items-center justify-between gap-3 p-2 z-10 relative border-b border-gray-200">
      <div className="flex items-center justify-start gap-2 relative">
        <div className="absolute left-0 z-10 flex items-center justify-start">
          {/* 隱藏歷史聊天 */}
          {/* {(!chatHistoryOpen || !isLargeScreen) && (
            <Button className="hover:bg-gray-100" variant="ghost" onClick={onToggleChatHistory}>
              {chatHistoryOpen ? (
                <PanelRightOpen className="size-5" />
              ) : (
                <PanelRightClose className="size-5" />
              )}
            </Button>
          )} */}
          <XELogoButton onClick={onNewThread} />
        </div>
      </div>

      <div className="flex items-center gap-4">
        <TooltipIconButton
          size="lg"
          className="p-4"
          tooltip="排班設定"
          variant="ghost"
          onClick={onOpenShiftSettings}
        >
          <Settings className="size-5" />
        </TooltipIconButton>
      </div>

      <div className="absolute inset-x-0 top-full h-5 bg-gradient-to-b from-background to-background/0" />
    </div>
  );
}
