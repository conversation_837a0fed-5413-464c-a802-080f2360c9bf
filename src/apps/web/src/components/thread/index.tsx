import { useCallback, useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Message } from '@langchain/langgraph-sdk';
import { useStreamContext } from '@/providers/Stream';
import { AssistantMessage, AssistantMessageLoading } from './messages/ai';
import { HumanMessage } from './messages/human';
import { DO_NOT_RENDER_ID_PREFIX } from '@/lib/ensure-tool-responses';

import { StickToBottom } from 'use-stick-to-bottom';
import { toast } from 'sonner';
import { useMediaQuery } from '@/hooks/useMediaQuery';

import { useDownloadFlow, usePostRobotComm, useFormSubmit } from './hooks';
import { useParentData } from '@/providers/ParentData';
import { ChatHeader, ChatFooter, ScrollToBottom, StickyToBottomContent } from './components';
import { ShiftSettingsModal } from '@/components/ui/shift-settings-modal';
import { useQueryState } from 'nuqs';
import { CommandPayload, CommandType, POST_MESSAGE_TYPES } from '@/lib/constants';
import { useMessageCenter } from '@/providers/ParentData';

/**
 * 主要的聊天界面組件
 * 已重構為使用自定義hooks和子組件，提升代碼可讀性和維護性
 */
export function Thread() {
  const [threadId, setThreadId] = useQueryState('threadId');
  const [isShiftSettingsOpen, setIsShiftSettingsOpen] = useState(false);
  const { sendMessage, isEmbedded } = useMessageCenter();

  const isLargeScreen = useMediaQuery('(min-width: 1024px)');

  const stream = useStreamContext();
  const messages = stream.messages;
  const isLoading = stream.isLoading;

  const lastError = useRef<string | undefined>(undefined);

  const { isWaitingForChildData, downloadWithChatData, downloadSchedule } = useDownloadFlow();

  const { requestProcessedData, processedData: parentData } = useParentData();

  usePostRobotComm(messages);

  const formSubmit = useFormSubmit({
    isLoading,
    streamMessages: messages,
    streamSubmit: stream.submit,
    streamStop: stream.stop,
  });

  // 追蹤是否已發起歡迎消息
  const hasTriggeredWelcome = useRef(false);
  // 追蹤是否有等待下載的請求
  const pendingDownload = useRef(false);
  // 追蹤已處理的 REFRESH 消息 ID，避免重複處理
  const processedRefreshIds = useRef(new Set<string>());

  useEffect(() => {
    if (!stream.error) {
      lastError.current = undefined;
      return;
    }
    try {
      const message = (stream.error as any).message;
      if (!message || lastError.current === message) {
        return;
      }

      lastError.current = message;
      toast.error('An error occurred. Please try again.', {
        description: (
          <p>
            <strong>Error:</strong> <code>{message}</code>
          </p>
        ),
        richColors: true,
        closeButton: true,
      });
    } catch {
      // no-op
    }
  }, [stream.error]);

  useEffect(() => {
    if (
      messages.length !== formSubmit.prevMessageLength.current &&
      messages?.length &&
      messages[messages.length - 1].type === 'ai'
    ) {
      formSubmit.setFirstTokenReceived(true);
    }
    if (messages.length !== formSubmit.prevMessageLength.current) {
      formSubmit.prevMessageLength.current = messages.length;
    }
    const lastMessage: (Message & { command?: CommandPayload }) | undefined =
      messages[messages.length - 1];

    // 檢查是否為 REFRESH 命令且尚未處理過
    if (
      lastMessage?.command?.type === CommandType.REFRESH &&
      lastMessage.id &&
      !processedRefreshIds.current.has(lastMessage.id)
    ) {
      sendMessage(POST_MESSAGE_TYPES.SCHEDULE_REFRESH, {});
      // 標記該消息已處理，避免重複觸發
      processedRefreshIds.current.add(lastMessage.id);
    }
  }, [messages, formSubmit]);

  const handleDownload = useCallback(() => {
    // 每次下載都向父視窗請求最新數據
    pendingDownload.current = true;
    downloadWithChatData(messages);
  }, [downloadWithChatData, messages]);

  // 移除自動下載邏輯，下載由用戶主動觸發

  // 當收到數據且有等待下載的請求時，執行下載
  useEffect(() => {
    if (parentData && pendingDownload.current) {
      downloadSchedule(parentData);
      pendingDownload.current = false; // 重置標誌
    }
  }, [parentData, downloadSchedule]);

  // 初始化時請求父視窗數據
  useEffect(() => {
    if (!messages.length && !hasTriggeredWelcome.current) {
      requestProcessedData([]);
    }
  }, [requestProcessedData, messages.length]);

  // 當收到父視窗數據且無現有消息時，發起歡迎消息
  useEffect(() => {
    if (!messages.length && parentData && !hasTriggeredWelcome.current) {
      console.log('發起歡迎消息，帶上父視窗數據:', parentData);

      formSubmit.handleSubmitWithWelcomeMessage('', 'ai', {
        type: CommandType.GREETING_SHIFT_SCHEDULE,
        payload: {
          departmentId: parentData?.departmentId,
          departmentName: parentData?.departmentName,
          companyName: parentData?.companyName,
          scheduleStartDate: parentData?.startDate,
          scheduleEndDate: parentData?.endDate,
        },
      });

      hasTriggeredWelcome.current = true;
    }
  }, [messages.length, parentData, formSubmit]);

  const chatStarted = !!threadId || !!messages.length;
  const hasNoAIOrToolMessages = !messages.find((m) => m.type === 'ai' || m.type === 'tool');

  // 排班設定模態框處理函數
  const handleOpenShiftSettings = useCallback(() => {
    setIsShiftSettingsOpen(true);
    if (isEmbedded) {
      sendMessage(POST_MESSAGE_TYPES.SCHEDULE_SETTINGS_FULL_SCREEN, {});
    }
  }, [isEmbedded, sendMessage]);

  const handleCloseShiftSettings = useCallback(() => {
    setIsShiftSettingsOpen(false);
    if (isEmbedded) {
      sendMessage(POST_MESSAGE_TYPES.SCHEDULE_SETTINGS_CLOSE_FULL_SCREEN, {});
    }
  }, [isEmbedded, sendMessage]);

  const renderMessages = () => (
    <>
      {messages
        .filter((m) => !m.id?.startsWith(DO_NOT_RENDER_ID_PREFIX))
        .map((message, index) => {
          if (message.type === 'human') {
            return (
              <HumanMessage
                key={message.id || `${message.type}-${index}`}
                message={message}
                isLoading={isLoading}
              />
            );
          }
          const hasToolCalls =
            message &&
            'tool_calls' in message &&
            message.tool_calls &&
            message.tool_calls.length > 0;
          const isPureAssistantMessage = message.type === 'ai' && !hasToolCalls;
          if (isPureAssistantMessage) {
            return (
              <AssistantMessage
                key={message.id || `${message.type}-${index}`}
                message={message}
                isLoading={isLoading}
                handleRegenerate={formSubmit.handleRegenerate}
                isWaitingForChildData={isWaitingForChildData}
                onDownload={handleDownload}
              />
            );
          }
          return <></>;
        })}

      {hasNoAIOrToolMessages && !!stream.interrupt && (
        <AssistantMessage
          key="interrupt-msg"
          message={undefined}
          isLoading={isLoading}
          handleRegenerate={formSubmit.handleRegenerate}
          isWaitingForChildData={isWaitingForChildData}
          onDownload={handleDownload}
        />
      )}

      {isLoading && <AssistantMessageLoading />}
    </>
  );

  const renderChatFooter = () => (
    <div className="sticky flex flex-col items-center gap-2 bottom-0 bg-white">
      <ScrollToBottom className="absolute bottom-full left-1/2 -translate-x-1/2 mb-4 animate-in fade-in-0 zoom-in-95" />

      <ChatFooter
        input={formSubmit.input}
        setInput={formSubmit.setInput}
        isLoading={isLoading}
        onSubmit={formSubmit.handleSubmit}
        onSubmitContent={formSubmit.createSubmitHandler}
        onStop={formSubmit.streamStop}
      />
    </div>
  );

  return (
    <div className="flex w-full h-screen overflow-hidden">
      {/*  */}
      {/* <div className="relative lg:flex hidden">
        <motion.div
          className="absolute h-full border-r bg-white overflow-hidden z-20"
          style={{ width: 300 }}
          animate={
            isLargeScreen ? { x: chatHistoryOpen ? 0 : -300 } : { x: chatHistoryOpen ? 0 : -300 }
          }
          initial={{ x: -300 }}
          transition={
            isLargeScreen ? { type: 'spring', stiffness: 300, damping: 30 } : { duration: 0 }
          }
        >
          <div className="relative h-full" style={{ width: 300 }}>
            <ThreadHistory />
          </div>
        </motion.div>
      </div> */}
      <motion.div
        className={cn(
          'flex-1 flex flex-col min-w-0 overflow-hidden relative',
          !chatStarted && 'grid-rows-[1fr]'
        )}
        layout={isLargeScreen}
        animate={
          {
            // marginLeft: chatHistoryOpen ? (isLargeScreen ? 300 : 0) : 0,
            // width: chatHistoryOpen ? (isLargeScreen ? 'calc(100% - 300px)' : '100%') : '100%',
          }
        }
        transition={
          isLargeScreen ? { type: 'spring', stiffness: 300, damping: 30 } : { duration: 0 }
        }
      >
        <ChatHeader
          chatStarted={chatStarted}
          onNewThread={() => setThreadId(null)}
          onOpenShiftSettings={handleOpenShiftSettings}
        />

        <StickToBottom className="relative flex-1 overflow-hidden">
          <StickyToBottomContent
            className={cn(
              'absolute inset-0 overflow-y-scroll [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-track]:bg-transparent',
              !chatStarted && 'flex flex-col items-stretch mt-[25vh]',
              chatStarted && 'grid grid-rows-[1fr_auto]'
            )}
            contentClassName="pt-8 pb-16 px-2 sm:px-4 max-w-full mx-auto flex flex-col gap-4 w-full min-w-0"
            content={renderMessages()}
            footer={renderChatFooter()}
          />
        </StickToBottom>
      </motion.div>

      {/* 排班設定模態框 */}
      <ShiftSettingsModal isOpen={isShiftSettingsOpen} onClose={handleCloseShiftSettings} />
    </div>
  );
}
