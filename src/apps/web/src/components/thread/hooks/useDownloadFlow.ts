import { useCallback } from 'react';
import { Message } from '@langchain/langgraph-sdk';
import { ShiftDataResponse } from '@/datas/api.types';
import { quickExportReact } from '@/lib/export-with-react';
import { useParentData } from '@/providers/ParentData';
import dayjs from 'dayjs';

/**
 * 下載流程處理 Hook
 * 專注於排班表下載的純邏輯處理
 */
export function useDownloadFlow() {
  const { requestProcessedData, processedData, isLoading } = useParentData();

  const downloadWithChatData = useCallback(
    (messages: Message[]) => {
      console.log('開始下載流程：請求父窗口資料...');
      requestProcessedData(messages);
    },
    [requestProcessedData]
  );

  const downloadSchedule = useCallback((data: ShiftDataResponse) => {
    if (data && data.employees && data.shiftsDefinition) {
      const title = `${data.companyName} 班表 - ${dayjs(data.startDate).format('YYYY/MM/DD')} ~ ${dayjs(data.endDate).format('YYYY/MM/DD')}`;
      quickExportReact.custom({
        apiData: data,
        title,
      });
      console.log('使用父窗口處理後的資料完成下載！');
    } else {
      console.log('父窗口未回傳資料，無法下載');
    }
  }, []);

  return {
    isWaitingForChildData: isLoading,
    downloadWithChatData,
    downloadSchedule,
    processedData,
  };
}
