import { useEffect } from 'react';
import { Message } from '@langchain/langgraph-sdk';
import { sendMessage, setupListener } from '@/lib/postRobot';
import { useMessageCenter } from '@/providers/ParentData';
import { POST_MESSAGE_TYPES } from '@/lib/constants';

/**
 * 窗口間通信處理 Hook (舊版本 - 保留用於向後兼容)
 * 負責處理postRobot相關的通信邏輯，包括監聽和響應父子窗口的消息
 */
export function usePostRobotComm(messages: Message[]) {
  // 監聽父窗口的資料請求
  useEffect(() => {
    const listener = setupListener('request-schedule-data', (data) => {
      console.log('收到父窗口的資料請求:', data);

      // 發送當前的 messages 資料給父窗口
      if (data.source) {
        sendMessage(data.source, 'schedule-data-response', {
          messages: messages,
          timestamp: new Date().toISOString(),
        });
      }
    });

    // 清理監聽器
    return () => {
      if (listener && typeof listener.cancel === 'function') {
        listener.cancel();
      }
    };
  }, [messages]);

  return {
    // 可以在這裡暴露一些通信相關的方法
    sendMessage: (target: Window, eventName: string, data: any) => {
      sendMessage(target, eventName, data);
    },
  };
}

/**
 * 窗口間通信處理 Hook (新版本 - 使用統一的 MessageCenter API)
 * 展示如何使用新的 MessageCenter API 重構現有代碼
 */
export function usePostRobotCommV2(messages: Message[]) {
  const { sendMessage, registerHandler, isEmbedded } = useMessageCenter();

  // 使用新的統一 API 監聽父窗口的資料請求
  useEffect(() => {
    if (!isEmbedded) {
      console.warn('不在嵌入環境中，跳過消息監聽');
      return;
    }

    const unregister = registerHandler(
      POST_MESSAGE_TYPES.REQUEST_SCHEDULE_DATA,
      async (data: any) => {
        console.log('收到父窗口的資料請求:', data);

        try {
          // 使用新的統一 API 發送回應
          await sendMessage(POST_MESSAGE_TYPES.SCHEDULE_DATA_RESPONSE, {
            messages: messages,
            timestamp: new Date().toISOString(),
          });
        } catch (error) {
          console.error('發送排班資料回應失敗:', error);
        }
      }
    );

    return unregister;
  }, [messages, sendMessage, registerHandler, isEmbedded]);

  return {
    // 使用新的統一 API 的方法
    sendMessage: async (type: string, data: any) => {
      try {
        await sendMessage(type as any, data);
      } catch (error) {
        console.error('發送消息失敗:', error);
      }
    },
    isEmbedded,
  };
}

/**
 * 完整的窗口間通信處理 Hook (推薦版本)
 * 提供完整的跨窗口通信功能，包括錯誤處理和健康檢查
 */
export function useEnhancedPostRobotComm(messages: Message[]) {
  const { sendMessage, registerHandler, isEmbedded, lastError, healthCheck } = useMessageCenter();

  // 監聽多種消息類型
  useEffect(() => {
    if (!isEmbedded) return;

    const unregisterScheduleData = registerHandler(
      POST_MESSAGE_TYPES.REQUEST_SCHEDULE_DATA,
      async (data: any) => {
        console.log('收到排班資料請求:', data);

        try {
          await sendMessage(POST_MESSAGE_TYPES.SCHEDULE_DATA_RESPONSE, {
            messages: messages,
            timestamp: new Date().toISOString(),
            totalMessages: messages.length,
          });
        } catch (error) {
          console.error('發送排班資料回應失敗:', error);
        }
      }
    );

    const unregisterHealthCheck = registerHandler(
      POST_MESSAGE_TYPES.HEALTH_CHECK,
      async (data: any) => {
        console.log('收到健康檢查請求:', data);

        try {
          await sendMessage(POST_MESSAGE_TYPES.HEALTH_CHECK_RESPONSE, {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            messageCount: messages.length,
          });
        } catch (error) {
          console.error('發送健康檢查回應失敗:', error);
        }
      }
    );

    return () => {
      unregisterScheduleData();
      unregisterHealthCheck();
    };
  }, [messages, sendMessage, registerHandler, isEmbedded]);

  // 自動健康檢查
  useEffect(() => {
    if (!isEmbedded) return;

    const interval = setInterval(async () => {
      try {
        await healthCheck();
      } catch (error) {
        console.error('自動健康檢查失敗:', error);
      }
    }, 30000); // 每30秒檢查一次

    return () => clearInterval(interval);
  }, [healthCheck, isEmbedded]);

  return {
    // 增強的 API
    sendMessage: async (type: string, data: any) => {
      if (!isEmbedded) {
        console.warn('不在嵌入環境中，消息未發送');
        return;
      }

      try {
        await sendMessage(type as any, data);
      } catch (error) {
        console.error('發送消息失敗:', error);
        throw error;
      }
    },

    // 發送排班刷新通知
    notifyScheduleRefresh: async () => {
      try {
        await sendMessage(POST_MESSAGE_TYPES.SCHEDULE_REFRESH, {
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error('發送排班刷新通知失敗:', error);
      }
    },

    // 手動健康檢查
    performHealthCheck: healthCheck,

    // 狀態
    isEmbedded,
    lastError,
    hasMessages: messages.length > 0,
  };
}

/**
 * 遷移指南：
 *
 * === 舊版本 ===
 * const { sendMessage } = usePostRobotComm(messages);
 *
 * === 新版本 ===
 * const { sendMessage, isEmbedded } = usePostRobotCommV2(messages);
 *
 * === 推薦版本 ===
 * const {
 *   sendMessage,
 *   notifyScheduleRefresh,
 *   performHealthCheck,
 *   isEmbedded,
 *   lastError
 * } = useEnhancedPostRobotComm(messages);
 *
 * === 優勢 ===
 * 1. 統一的消息類型管理
 * 2. 自動的嵌入環境檢測
 * 3. 完整的錯誤處理
 * 4. 健康檢查機制
 * 5. 更好的 TypeScript 支持
 */
