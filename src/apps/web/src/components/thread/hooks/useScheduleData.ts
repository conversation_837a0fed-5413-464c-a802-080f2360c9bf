import { useCallback } from 'react';
import { Message } from '@langchain/langgraph-sdk';
import { ShiftItem, Employee as LocalEmployee } from '@/components/ui/shift/shift-constants';

/**
 * 排班數據處理 Hook
 * 負責從聊天消息中提取排班相關數據
 */
export function useScheduleData() {
  const extractScheduleDataFromMessages = useCallback((messages: Message[]) => {
    const extractedShifts: ShiftItem[] = [];
    const extractedEmployees: LocalEmployee[] = [];
    let extractedTitle = '';

    // 遍歷 messages 尋找 AI 生成的排班資料
    messages.forEach((message) => {
      if (message.type === 'ai' && message.content) {
        // 確保 content 是字符串
        const content = typeof message.content === 'string' ? message.content : JSON.stringify(message.content);
        
        // 提取標題
        const titleMatch = content.match(/排班表[：:]\s*(.+)/);
        if (titleMatch && !extractedTitle) {
          extractedTitle = titleMatch[1].trim();
        }
        
        // 提取員工資訊
        const employeeMatches = content.match(/員工[：:]\s*(.+)/g);
        if (employeeMatches) {
          employeeMatches.forEach((match: string, index: number) => {
            const name = match.replace(/員工[：:]\s*/, '').trim();
            const employeeNumber = `EXTRACT-${index + 1}`;
            
            // 創建完整的 Employee 物件以符合 API 資料結構
            const employee: LocalEmployee = {
              chineseName: name,
              employeeNumber: employeeNumber,
              schedulingStatus: 1,
              totalMonthLeaveTime: 0,
              totalScheduleLeaveTime: 0,
              fatigueValue: 0,
              schedulingStatusRangeMap: {},
              empSchedulingStartDate: "0001-01-01T00:00:00+00:00",
              empSchedulingEndDate: "0001-01-01T00:00:00+00:00",
              supervisorSchedulingStartDate: "0001-01-01T00:00:00+00:00",
              supervisorSchedulingEndDate: "0001-01-01T00:00:00+00:00",
              selectShiftSchedule: true,
              arrangeLeave: true,
              isShiftRules: true,
              adjustmentScheduleTime: true,
              advanceLeave: true,
              isEditable: true,
              isSupervisorEditable: true,
              calendars: [],
              totalWorkTime: 0,
              totalLeaveTime: 0,
              totalOverTime: 0,
              totalTripMinutes: 0,
              monthLeaveDays: 0,
              monthLeaveDaysUsed: 0,
              // 向下相容屬性
              id: employeeNumber,
              name: name
            };
            
            extractedEmployees.push(employee);
          });
        }
        
        // 這裡可以添加更複雜的班次提取邏輯
        // 例如：解析時間、班次類型等
      }
    });

    return {
      shifts: extractedShifts,
      employees: extractedEmployees.length > 0 ? extractedEmployees : undefined,
      title: extractedTitle || undefined
    };
  }, []);

  return {
    extractScheduleDataFromMessages
  };
} 