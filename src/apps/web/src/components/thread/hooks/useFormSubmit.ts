import { useState, useCallback, useRef, FormEvent } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Message, Checkpoint } from '@langchain/langgraph-sdk';
import { ensureToolCallsHaveResponses } from '@/lib/ensure-tool-responses';

interface UseFormSubmitProps {
  isLoading: boolean;
  streamMessages: Message[];
  streamSubmit: (data: any, options: any) => void;
  streamStop: () => void;
}

/**
 * 表單提交處理 Hook
 * 負責處理消息提交、重新生成等相關邏輯
 */
export function useFormSubmit({
  isLoading,
  streamMessages,
  streamSubmit,
  streamStop,
}: UseFormSubmitProps) {
  const [input, setInput] = useState('');
  const [firstTokenReceived, setFirstTokenReceived] = useState(false);

  // TODO: this should be part of the useStream hook
  const prevMessageLength = useRef(0);

  const handleSubmit = useCallback(
    (e: FormEvent) => {
      e.preventDefault();
      if (!input.trim() || isLoading) return;
      setFirstTokenReceived(false);

      const newHumanMessage: Message = {
        id: uuidv4(),
        type: 'human',
        content: input,
      };

      const toolMessages = ensureToolCallsHaveResponses(streamMessages);
      streamSubmit(
        { messages: [...toolMessages, newHumanMessage] },
        {
          streamMode: ['values'],
          optimisticValues: (prev: any) => ({
            ...prev,
            messages: [...(prev.messages ?? []), ...toolMessages, newHumanMessage],
          }),
        }
      );

      setInput('');
    },
    [input, isLoading, streamMessages, streamSubmit]
  );

  // 合并后的共用函数，接受消息内容作为参数
  const handleSubmitWithContent = useCallback(
    (content: string) => {
      if (isLoading) return;
      setFirstTokenReceived(false);

      const newHumanMessage: Message = {
        id: uuidv4(),
        type: 'human',
        content,
      };

      const toolMessages = ensureToolCallsHaveResponses(streamMessages);
      streamSubmit(
        { messages: [...toolMessages, newHumanMessage] },
        {
          streamMode: ['values'],
          optimisticValues: (prev: any) => ({
            ...prev,
            messages: [...(prev.messages ?? []), ...toolMessages, newHumanMessage],
          }),
        }
      );
    },
    [isLoading, streamMessages, streamSubmit]
  );

  // 創建包裝函數來處理特定內容的提交
  const createSubmitHandler = useCallback(
    (content: string) => {
      return () => handleSubmitWithContent(content);
    },
    [handleSubmitWithContent]
  );

  const handleRegenerate = useCallback(
    (parentCheckpoint: Checkpoint | null | undefined) => {
      // Do this so the loading state is correct
      prevMessageLength.current = prevMessageLength.current - 1;
      setFirstTokenReceived(false);
      streamSubmit(undefined, {
        checkpoint: parentCheckpoint,
        streamMode: ['values'],
      });
    },
    [streamSubmit]
  );

  const handleSubmitWithWelcomeMessage = useCallback(
    (content: string, messageType: 'ai' | 'human', command?: { type: string; payload?: any }) => {
      if (isLoading) return;
      setFirstTokenReceived(false);
      console.log('command', command);

      const newMessage: Message = {
        id: uuidv4(),
        type: messageType,
        content,
        ...(command && { command }),
      };

      const toolMessages = ensureToolCallsHaveResponses(streamMessages);
      streamSubmit(
        { messages: [...toolMessages, newMessage] },
        {
          streamMode: ['values'],
          optimisticValues: (prev: any) => ({
            ...prev,
            messages: [...(prev.messages ?? []), ...toolMessages, newMessage],
          }),
        }
      );
    },
    [isLoading, streamMessages, streamSubmit]
  );

  return {
    input,
    setInput,
    firstTokenReceived,
    setFirstTokenReceived,
    prevMessageLength,
    handleSubmit,
    handleSubmitWithContent,
    createSubmitHandler,
    handleRegenerate,
    handleSubmitWithWelcomeMessage,
    isLoading,
    streamStop,
  };
}
