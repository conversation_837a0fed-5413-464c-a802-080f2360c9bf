import React, { useRef, useState, useEffect } from 'react';
import ShiftSchedulerTemplate from './shift-scheduler-template';
import { ShiftDataResponse } from '../../../datas/api.types';
import apiData from '../../../datas/api.json';

const ShiftSchedulerPage: React.FC = () => {
  const contentRef = useRef<HTMLDivElement>(null);
  const [shiftData, setShiftData] = useState<ShiftDataResponse | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // 載入 API 資料
    try {
      // 驗證資料結構
      if (apiData && apiData.type === 'shift-data') {
        setShiftData(apiData as unknown as ShiftDataResponse);
      } else {
        setError('API 資料格式不正確');
      }
    } catch (err) {
      setError('載入 API 資料時發生錯誤');
    } finally {
      setIsLoading(false);
    }
  }, []);

  if (isLoading) {
    return (
      <div className="h-screen w-full overflow-hidden p-[15px] flex items-center justify-center">
        <div className="text-lg">載入班表資料中...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen w-full overflow-hidden p-[15px] flex items-center justify-center">
        <div className="text-red-500">
          <h3 className="text-lg font-semibold mb-2">錯誤</h3>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen w-full overflow-hidden p-[15px]">
      <div className="w-full h-full flex flex-col">
        {/* 班表資訊 */}
        {shiftData && (
          <div className="mb-4 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold mb-2">班表資訊</h2>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">員工數量：</span>
                {shiftData.employees.length} 人
              </div>
              <div>
                <span className="font-medium">班次類型：</span>
                {shiftData.shiftsDefinition.shifts.length} 種
              </div>
              <div>
                <span className="font-medium">資料類型：</span>
                {shiftData.type}
              </div>
            </div>
          </div>
        )}
        
        {/* 排班表內容 */}
        <div ref={contentRef} className="flex-1 overflow-auto" data-schedule-content>
          <ShiftSchedulerTemplate 
            apiData={shiftData}
            title="員工排班表"
          />
        </div>
      </div>
    </div>
  );
};

export default ShiftSchedulerPage; 