import React from 'react';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import 'dayjs/locale/zh-tw';
import ShiftSchedulerTemplate from './shift-scheduler-template';
import { Employee, ShiftItem, defaultEmployees } from './shift-constants';
import { SchedulerHeader } from './shift-scheduler-template';
import { ShiftDataResponse } from '../../../datas/api.types';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.locale('zh-tw');

// 組件props介面
interface ShiftSchedulerExportProps {
  currentMonth?: dayjs.Dayjs;
  apiData?: ShiftDataResponse;
  shifts?: ShiftItem[];
  employees?: Employee[];
  title?: string;
}

import styles from '@/index.css?inline';

/**
 * 內聯樣式組件 - 使用 Tailwind CSS
 */
const ExportStyles: React.FC = () => (
  <style
    dangerouslySetInnerHTML={{
      __html: `
      ${styles}
      
      /* 基礎樣式 */
      * {
        box-sizing: border-box;
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        background-color: #f9fafb;
        line-height: 1.5;
        margin: 20px;
      }
      

      
      /* 打印樣式 */
      @media print {
        body { 
          margin: 0; 
          font-size: 12px;
        }
        .no-print { 
          display: none !important; 
        }
      }
    `,
    }}
  />
);

/**
 * 轉換 Template 組件為 Export 樣式的包裝組件
 */
interface ExportTemplateWrapperProps {
  currentMonth?: dayjs.Dayjs;
  apiData?: ShiftDataResponse;
  shifts?: ShiftItem[];
  employees?: Employee[];
}

const ExportTemplateWrapper: React.FC<ExportTemplateWrapperProps> = (props) => {
  return (
    <div className="flex-1 max-h-[800px] overflow-hidden">
      <ShiftSchedulerTemplate {...props} />
    </div>
  );
};

/**
 * 主要的班表組件
 */
const ShiftSchedulerExport: React.FC<ShiftSchedulerExportProps> = ({
  currentMonth = dayjs(),
  apiData,
  shifts = [],
  employees = defaultEmployees,
  title,
}) => {
  const displayTitle = title || `班表 ${apiData?.startDate} ~ ${apiData?.endDate}`;

  return (
    <html lang="zh-TW">
      <head>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>{displayTitle}</title>
        <ExportStyles />
      </head>
      <body>
        <SchedulerHeader title={displayTitle} />
        {/* 導出資訊 */}
        <div className="text-right text-xs text-gray-500 mb-5 flex justify-between items-center">
          <div className="flex flex-col justify-center items-start">
            <div>單位: {apiData?.departmentName}</div>
            <p className="text-sm text-gray-600">
              排班起迄：{apiData?.startDate} ~ {apiData?.endDate}
            </p>
          </div>
          <p>此檔案由系統自動生成</p>
        </div>

        {/* 班表內容 - 使用 Template 組件 */}
        <ExportTemplateWrapper
          currentMonth={currentMonth}
          apiData={apiData}
          shifts={shifts}
          employees={employees}
        />

        <script
          dangerouslySetInnerHTML={{
            __html: `
            document.addEventListener('DOMContentLoaded', function() {
              console.log('班表已載入 - ${displayTitle}');
            });
          `,
          }}
        />
      </body>
    </html>
  );
};

export default ShiftSchedulerExport;
