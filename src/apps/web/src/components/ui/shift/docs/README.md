# 班次排程管理系統 (Shift Scheduler)

一個基於 React + TypeScript 的企業級班次排程管理系統，提供完整的員工排班、統計分析和排程視覺化功能。

## 📁 檔案結構

```
src/components/ui/shift/
├── docs/
│   └── README.md                    # 此文檔
├── shift-business-logic.ts          # 核心業務邏輯
├── shift-constants.ts               # 常數定義與工具函數
├── shift-render-components.tsx      # UI 渲染組件
├── shift-stats-components.tsx       # 統計相關組件
├── shift-scheduler-template.tsx     # 主要模板組件
├── shift-scheduler-page.tsx         # 頁面級組件
└── shift-scheduler-export.tsx       # 匯出功能組件
```

## 🏗️ 架構設計

### 核心設計原則

1. **關注點分離 (Separation of Concerns)**
   - 業務邏輯與 UI 邏輯分離
   - 純函數設計，便於測試
   - 組件職責單一化

2. **模組化設計 (Modular Design)**
   - 按功能領域劃分模組
   - 可重用的組件設計
   - 清晰的依賴關係

3. **TypeScript 型別安全**
   - 完整的型別定義
   - 介面驅動開發
   - 編譯時錯誤檢查

## 📋 功能特性

### ✨ 主要功能

- **班次排程管理**：支援多種班次類型的排程安排
- **員工排班視覺化**：直觀的月曆式排班表顯示
- **統計分析**：即時的出勤統計與分析
- **支援排班**：支援部門間的人力調度
- **請假管理**：整合請假單與出差申請
- **調班標記**：顯示班次調整記錄
- **匯出功能**：支援班表的匯出與列印

### 🎯 班次類型支援

- **工作班次**：早班、中班、晚班等正常工作排程
- **休假類型**：休息日、例假日、國定假日
- **請假類型**：個人假、病假、特休等
- **特殊狀態**：出差、支援、調班等

## 📁 模組詳細說明

### 1. shift-business-logic.ts - 核心業務邏輯

**主要職責**：處理所有班次相關的業務邏輯計算

#### 核心函數

```typescript
// 班次狀態判斷
determineShiftStatus(dayCalendar): ShiftStatusResult

// 班次分類計算
getShiftCategory(calendarDay): ShiftCategory

// 渲染條件判斷
getShiftRenderConditions(calendar, shiftSchedule): ShiftRenderConditions

// 統計計算
calculatePersonalStats(calendars): AttendanceStats
calculateTotalStats(employees): AttendanceStats
calculateDailyAttendanceCount(date, employees): number
```

#### 常數定義

- `SHIFT_CONSTANTS`：包含所有班次相關的魔術數字和識別碼
- 事件狀態常數（NORMAL, HOLIDAY, NO_SCHEDULE等）
- 循環狀態常數（WORK, HOLIDAY）
- 特殊班次 ID（節假日、休息日等）

### 2. shift-constants.ts - 常數與工具函數

**主要職責**：定義型別介面、預設資料和工具函數

#### 主要內容

```typescript
// 型別定義
interface Employee extends ApiEmployee
interface ShiftType extends ShiftDefinition
interface ShiftItem extends CalendarDay

// 資料轉換工具
convertApiDataToComponent(apiData): ComponentData
extractEmployeesFromApi(apiData): Employee[]
extractShiftTypesFromApi(apiData): ShiftType[]

// 格式化工具
formatTimeFromISO(isoTime): string
getShiftColor(shiftSchedule): string
getShiftTitle(shiftSchedule): string
```

### 3. shift-render-components.tsx - UI 渲染組件

**主要職責**：提供所有班次相關的 UI 組件

#### 組件列表

- **基礎組件**
  - `ShiftBadge`：基本班次標籤
  - `ShiftContainer`：班次容器
  - `AdjustmentMark`：調班標記

- **工作班次組件**
  - `WorkDayShift`：工作日班次（支援休息時間分割顯示）

- **狀態標籤組件**
  - `DayOffBadge`：補休標籤
  - `MonthLeaveBadge`：全月請假標籤
  - `RestDayBadge`：休息日標籤
  - `UsualHolidayBadge`：一般假期標籤
  - `LeaveBadge`：請假標籤
  - `TripBadge`：出差標籤
  - `SupportBadge`：支援部門標籤
  - `NoScheduleBadge`：無排班標籤
  - `HolidayBadge`：節假日標籤

### 4. shift-stats-components.tsx - 統計組件

**主要職責**：處理統計資料的顯示

#### 組件架構

- **原子組件**
  - `StatsNumber`：統計數字顯示
  - `StatsLabel`：統計標籤
  - `StatsColumn`：統計欄位

- **組合組件**
  - `EmployeeStatsCell`：員工統計單元格
  - `TotalStats`：總體統計
  - `DailyAttendance`：每日出勤統計
  - `StatsHeader`：統計標題
  - `StatsHeaderRow`：統計標題行

### 5. shift-scheduler-template.tsx - 主要模板

**主要職責**：組合所有功能，提供完整的班表顯示

#### 主要組件

- `SchedulerHeader`：班表標題
- `CalendarHeader`：日曆表頭
- `AttendanceStats`：出勤統計行
- `DayCell`：日期單元格
- `EmployeeRow`：員工行
- `ShiftSchedulerTemplate`：主要模板組件

### 6. shift-scheduler-page.tsx - 頁面組件

**主要職責**：提供完整的頁面級別組件

- 資料載入與錯誤處理
- 頁面佈局管理
- API 資料整合

### 7. shift-scheduler-export.tsx - 匯出功能

**主要職責**：提供班表匯出和列印功能

- HTML 格式匯出
- 列印樣式最佳化
- 內聯樣式處理

## 🚀 使用方法

### 基本使用

```typescript
import ShiftSchedulerTemplate from './shift-scheduler-template';
import { ShiftDataResponse } from '../../../datas/api.types';

// 使用 API 資料
const MyComponent = () => {
  const [apiData, setApiData] = useState<ShiftDataResponse>();
  
  return (
    <ShiftSchedulerTemplate 
      apiData={apiData}
      title="員工排班表"
    />
  );
};
```

### 自訂資料使用

```typescript
import ShiftSchedulerTemplate from './shift-scheduler-template';
import { Employee, ShiftItem } from './shift-constants';

const MyComponent = () => {
  const employees: Employee[] = [...];
  const shifts: ShiftItem[] = [...];
  
  return (
    <ShiftSchedulerTemplate 
      employees={employees}
      shifts={shifts}
      startDate={dayjs('2024-01-01')}
      endDate={dayjs('2024-01-31')}
    />
  );
};
```

### 匯出功能

```typescript
import ShiftSchedulerExport from './shift-scheduler-export';

const ExportComponent = () => (
  <ShiftSchedulerExport 
    apiData={apiData}
    title="2024年1月排班表"
  />
);
```

## 🎨 樣式與主題

### CSS 類別規範

- 使用 Tailwind CSS 作為主要樣式框架
- 遵循響應式設計原則
- 支援列印樣式最佳化

### 顏色系統

```typescript
// 班次顏色對應
const colorMapping = {
  work: '#4F46E5',      // 工作班次（藍色）
  leave: '#F59E0B',     // 請假（橙色）
  rest: '#10B981',      // 休息（綠色）
  support: '#8B5CF6',   // 支援（紫色）
  holiday: '#EF4444',   // 假期（紅色）
};
```

## 📊 資料結構

### API 資料格式

```typescript
interface ShiftDataResponse {
  type: string;
  startDate: string;
  endDate: string;
  departmentName: string;
  companyName: string;
  employees: Employee[];
  shiftsDefinition: {
    shifts: ShiftDefinition[];
    monthLeaves: ShiftDefinition[];
  };
}
```

### 員工資料結構

```typescript
interface Employee {
  employeeNumber: string;
  chineseName: string;
  schedulingStatus: number;
  calendars: CalendarDay[];
  // ... 其他屬性
}
```

### 班次資料結構

```typescript
interface CalendarDay {
  date: string;
  schedulingStatus: number;
  shiftSchedule?: ShiftSchedule;
  calendarEvent?: CalendarEvent;
  leaveSheets?: LeaveSheet[];
  tripSheets?: TripSheet[];
  supportDeptId?: string;
  // ... 其他屬性
}
```

## 🧪 測試指南

### 單元測試

```typescript
// 測試業務邏輯函數
import { determineShiftStatus, calculatePersonalStats } from './shift-business-logic';

describe('shift-business-logic', () => {
  test('determineShiftStatus should return correct status', () => {
    const mockCalendar = { /* ... */ };
    const result = determineShiftStatus(mockCalendar);
    expect(result.isHaveSchedule).toBe(true);
  });
});
```

### 組件測試

```typescript
// 測試渲染組件
import { render, screen } from '@testing-library/react';
import { WorkDayShift } from './shift-render-components';

test('WorkDayShift renders correctly', () => {
  const mockShiftSchedule = { /* ... */ };
  render(<WorkDayShift shiftSchedule={mockShiftSchedule} />);
  expect(screen.getByText('早班')).toBeInTheDocument();
});
```

## 🔧 設定與客製化

### 環境變數

```bash
# 開發環境設定
REACT_APP_API_BASE_URL=http://localhost:3000/api
REACT_APP_DEFAULT_TIMEZONE=Asia/Taipei
```

### 客製化班次類型

```typescript
// 在 shift-constants.ts 中修改
export const customShiftTypes: ShiftType[] = [
  {
    id: 'custom-shift',
    title: '自訂班次',
    color: '#FF6B6B',
    startTime: '10:00',
    endTime: '18:00',
  },
];
```

## 🚨 注意事項

### 效能考量

1. **大量資料處理**：當員工數量超過 100 人時，建議使用虛擬捲動
2. **記憶體管理**：避免在渲染函數中創建新物件
3. **重複渲染**：使用 React.memo 和 useMemo 最佳化

### 瀏覽器相容性

- 支援 Chrome 80+
- 支援 Firefox 75+
- 支援 Safari 13+
- 支援 Edge 80+

### 已知限制

1. 不支援即時同步更新
2. 匯出功能需要現代瀏覽器支援
3. 列印功能在某些瀏覽器中可能需要額外設定

## 📈 未來規劃

### 待開發功能

- [ ] 拖放式排班編輯
- [ ] 班次衝突檢測
- [ ] 排班規則引擎
- [ ] 行動裝置最佳化
- [ ] 即時通知系統
- [ ] 排班範本功能

### 效能最佳化

- [ ] 虛擬捲動實作
- [ ] 資料分頁載入
- [ ] 快取機制改善
- [ ] Bundle 大小最佳化

## 🤝 貢獻指南

### 開發流程

1. Fork 專案
2. 建立功能分支 (`git checkout -b feature/new-feature`)
3. 提交變更 (`git commit -am 'Add new feature'`)
4. 推送到分支 (`git push origin feature/new-feature`)
5. 建立 Pull Request

### 程式碼規範

- 遵循 ESLint 設定
- 使用 Prettier 格式化
- 撰寫 TypeScript 型別定義
- 添加適當的註解說明

---

**維護者**：開發團隊  
**最後更新**：2024年12月  
**版本**：1.0.0 