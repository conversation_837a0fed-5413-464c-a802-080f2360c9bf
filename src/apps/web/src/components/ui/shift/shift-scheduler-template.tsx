import React from 'react';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import 'dayjs/locale/zh-tw';
import { ShiftType, Employee, ShiftItem, defaultEmployees } from './shift-constants';
import { ShiftDataResponse, CalendarDay } from '@/datas/api.types';
import { determineShiftStatus, getShiftRenderConditions } from './shift-business-logic';
import {
  WorkDayShift,
  DayOffBadge,
  MonthLeaveBadge,
  RestDayBadge,
  UsualHolidayBadge,
  LeaveBadge,
  TripBadge,
  SupportBadge,
  NoScheduleBadge,
  HolidayBadge,
  AdjustmentMark,
  ShiftContainer,
} from './shift-render-components';
import {
  EmployeeStatsCell,
  TotalStats,
  DailyAttendance,
  StatsHeader,
  StatsHeaderRow,
} from './shift-stats-components';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.locale('zh-tw');

// 組件props介面
interface ShiftSchedulerTemplateProps {
  startDate?: dayjs.Dayjs;
  endDate?: dayjs.Dayjs;
  apiData?: ShiftDataResponse; // 新增：接收完整的 API 資料
  shifts?: ShiftItem[];
  employees?: Employee[];
  shiftTypes?: ShiftType[];
  className?: string;
  title?: string;
}

/**
 * 班表標題 Header 組件
 */
interface SchedulerHeaderProps {
  title?: string;
}

export const SchedulerHeader: React.FC<SchedulerHeaderProps> = ({ title }) => {
  return (
    <div className="bg-slate-700 text-white px-4 py-3">
      <div className="flex justify-between items-center">
        {/* 左側：Apollo logo */}
        <div className="flex items-center min-w-0 flex-1">
          <div className="text-blue font-semibold text-xl">Apollo</div>
        </div>

        {/* 中間：頁面標題 */}
        <div className="flex-1 text-center">
          {title && <h1 className="text-2xl font-bold text-white">{title}</h1>}
        </div>

        {/* 右側：產出時間 */}
        <div className="min-w-0 flex-1 text-right">
          <div className="text-white text-sm">產出時間：{dayjs().format('YYYY/MM/DD HH:mm')}</div>
        </div>
      </div>
    </div>
  );
};

/**
 * 日曆表頭元件
 * 顯示日期與星期
 */
interface CalendarHeaderProps {
  days: string[]; // 日期陣列
}

const CalendarHeader: React.FC<CalendarHeaderProps> = ({ days }) => {
  // 星期對應表
  const weekdayMap = {
    0: '日',
    1: '一',
    2: '二',
    3: '三',
    4: '四',
    5: '五',
    6: '六',
  };

  // 檢查是否是該跨月份區間的第一天
  const isFirstDayOfMonth = (day: string, index: number) => {
    const dayObj = dayjs(day);
    if (index === 0) return true; // 第一個日期總是顯示

    const prevDay = dayjs(days[index - 1]);
    return dayObj.month() !== prevDay.month();
  };

  return (
    <div className="sticky top-0 z-[999999] bg-gray-100" role="row" aria-rowindex={1}>
      {/* 日期和星期行 */}
      <div className="flex">
        <div
          className="sticky left-0 z-[9999] min-w-[200px] bg-white p-2 font-medium"
          role="columnheader"
        />
        <StatsHeader />

        {days.map((day, index) => {
          const dayObj = dayjs(day);
          const showMonthTitle = isFirstDayOfMonth(day, index);

          return (
            <div
              key={day}
              className={`min-w-[90px] flex flex-col items-center justify-end flex-1 bg-white`}
              role="columnheader"
              aria-colindex={index + 3}
              data-date={day}
            >
              {showMonthTitle && (
                <div className="text-xs font-medium text-center py-1 text-blue">
                  {dayObj.format('YYYY年MM月')}
                </div>
              )}
              <div
                className={`flex justify-start items-center gap-4 w-full relative ${index !== days.length - 1 ? 'after:absolute after:right-0 after:top-1/4 after:bottom-1/4 after:w-px after:bg-gray-300' : ''}`}
              >
                <div className="text-xs text-gray-500 ml-3">
                  {weekdayMap[dayObj.day() as keyof typeof weekdayMap]}
                </div>
                <div>{dayObj.date()}</div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

/**
 * 統計行組件
 */
interface AttendanceStatsProps {
  days: string[];
  employees: Employee[];
  departmentName: string;
}

const AttendanceStats: React.FC<AttendanceStatsProps> = ({ days, employees, departmentName }) => {
  const title = (
    <div className="flex justify-between items-center w-full">
      <div className="text-xs text-dark-800">{departmentName}</div>
      <div className="text-xs bg-blue rounded-l-md p-1">統計: 時/人</div>
    </div>
  );
  return (
    <div className="sticky z-[999999] flex bg-gray-200 top-[50px]">
      <StatsHeaderRow title={title} />
      <TotalStats employees={employees} />

      {days.map((day) => (
        <DailyAttendance key={day} date={day} employees={employees} />
      ))}
    </div>
  );
};

/**
 * 日期單元格元件
 */
interface DayCellProps {
  day: string;
  employee: Employee;
}

const DayCell: React.FC<DayCellProps> = ({ day, employee }) => {
  const dayObj = dayjs(day);
  const isWeekend = dayObj.day() === 0 || dayObj.day() === 6;

  // 找到該員工該日期的行事曆資料
  const dayCalendar = employee.calendars.find(
    (calendar) => dayjs(calendar.date).format('YYYY-MM-DD') === day
  );

  // 渲染有排班情況
  const renderHaveSchedule = (calendar: CalendarDay) => {
    const { shiftSchedule, tripSheets, leaveSheets } = calendar;

    if (!shiftSchedule) return null;

    const conditions = getShiftRenderConditions(calendar, shiftSchedule);

    // 1. 節假日特例
    if (conditions.isHolidaySpecial) {
      return <HolidayBadge />;
    }

    // 2. 無排班（事件在 3 or 4，且循環狀態為 2）
    if (conditions.isNoScheduleWithCycle2) {
      return <NoScheduleBadge />;
    }

    // 3. 其餘條件下的排班組件
    const nodes: React.ReactNode[] = [];

    // 午／夜班（cycleStatus !== 2）
    if (conditions.shouldShowWorkDay) {
      nodes.push(<WorkDayShift key="work-day" shiftSchedule={shiftSchedule} />);
    }

    // 補休
    if (conditions.shouldShowDayOff) {
      nodes.push(<DayOffBadge key="day-off" />);
    }

    // 全月請假
    if (conditions.shouldShowMonthLeave) {
      nodes.push(<MonthLeaveBadge key="month-leave" />);
    }

    // 休息日
    if (conditions.shouldShowRestDay) {
      nodes.push(<RestDayBadge key="rest-day" />);
    }

    // 一般假期
    if (conditions.shouldShowUsualHoliday) {
      nodes.push(<UsualHolidayBadge key="usual-holiday" />);
    }

    // 調班標記
    if (conditions.shouldShowAdjustmentMark) {
      nodes.push(<AdjustmentMark key="adjustment" />);
    }

    // 請假單
    if (leaveSheets && leaveSheets.length > 0) {
      nodes.push(<LeaveBadge key="leave" />);
    }

    // 出差
    if (tripSheets && tripSheets.length > 0) {
      nodes.push(<TripBadge key="trip" />);
    }

    return <ShiftContainer>{nodes}</ShiftContainer>;
  };

  // 渲染邏輯
  const renderCellContent = () => {
    const shiftStatus = determineShiftStatus(dayCalendar);

    if (shiftStatus.isHaveSchedule) {
      return renderHaveSchedule(dayCalendar!);
    }

    if (shiftStatus.isSupportSchedule) {
      return <SupportBadge />;
    }

    if (shiftStatus.isNoSchedule) {
      return <NoScheduleBadge />;
    }

    if (shiftStatus.isHoliday) {
      return <HolidayBadge />;
    }

    if (shiftStatus.isDayOff) {
      return <DayOffBadge />;
    }

    if (shiftStatus.isMonthLeave) {
      return <MonthLeaveBadge />;
    }

    if (shiftStatus.isRestDay) {
      return <RestDayBadge />;
    }

    if (shiftStatus.isUsualHoliday) {
      return <UsualHolidayBadge />;
    }

    return null;
  };

  return (
    <div
      className={`min-w-[90px] flex-1 border border-gray-300 p-1 ${isWeekend ? 'bg-gray-100' : ''}`}
      data-employee-id={employee.employeeNumber}
      data-date={day}
      style={{ minHeight: '40px' }}
    >
      <div className="flex flex-col gap-0.5">{renderCellContent()}</div>
    </div>
  );
};

/**
 * 員工行元件
 */
interface EmployeeRowProps {
  employee: Employee;
  days: string[];
}

const EmployeeRow: React.FC<EmployeeRowProps> = ({ employee, days }) => {
  return (
    <div className="flex">
      <div className="sticky left-0 z-[9999] flex flex-col min-w-[200px] items-center justify-center border-b border-gray-300 last:border-b-0 bg-white p-2">
        <div className="text-xs text-dark-800">{employee.chineseName || employee.name}</div>
        <div className="text-xs text-dark-800">{employee.employeeNumber}</div>
      </div>

      {/* 員工統計單元格 */}
      <EmployeeStatsCell employee={employee} />

      {days.map((day) => (
        <DayCell key={day} day={day} employee={employee} />
      ))}
    </div>
  );
};

/**
 * 主要的班表 Template 組件
 */
const ShiftSchedulerTemplate: React.FC<ShiftSchedulerTemplateProps> = ({
  apiData, // 優先使用 API 資料
  employees = defaultEmployees,
  className = '',
}) => {
  // 如果有 API 資料，使用 API 資料，否則使用傳入的 props
  const actualEmployees = apiData ? apiData.employees : employees;
  const startDate = apiData ? dayjs(apiData.startDate) : dayjs();
  const endDate = apiData ? dayjs(apiData.endDate) : dayjs().endOf('month');

  // 生成指定日期區間的所有日期
  const getDaysInRange = () => {
    const days = [];
    let currentDate = startDate;

    while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
      days.push(currentDate.format('YYYY-MM-DD'));
      currentDate = currentDate.add(1, 'day');
    }

    return days;
  };

  const days = getDaysInRange();

  return (
    <div className={`flex flex-col h-full ${className}`}>
      <div
        className="flex-1 overflow-auto max-h-[800px] scrollbar-none border border-dark-400 shadow-lg rounded-lg"
        data-schedule-content
      >
        <div className="flex min-w-fit flex-col">
          <CalendarHeader days={days} />
          <AttendanceStats
            days={days}
            employees={actualEmployees}
            departmentName={apiData?.departmentName || ''}
          />

          <div className="flex-1">
            {actualEmployees.map((employee: Employee) => (
              <EmployeeRow key={employee.employeeNumber} employee={employee} days={days} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShiftSchedulerTemplate;
export type { ShiftSchedulerTemplateProps, SchedulerHeaderProps };
