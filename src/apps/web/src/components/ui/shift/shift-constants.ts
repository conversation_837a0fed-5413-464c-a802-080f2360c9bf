import { 
  Employee as ApiEmployee, 
  ShiftDataResponse, 
  CalendarDay, 
  ShiftSchedule,
  ShiftDefinition 
} from '../../../datas/api.types';

// 重新定義以符合 API 資料結構，添加必要的額外屬性
export interface Employee extends ApiEmployee {
  // 為了向下相容，添加舊版本需要的屬性
  id?: string;
  name?: string;
}

// 班次類型介面 - 對應 API 中的 ShiftDefinition，添加向下相容屬性
export interface ShiftType extends ShiftDefinition {
  // 保持向下相容的額外屬性
  title?: string;
  id?: string;
  color?: string;
  startTime?: string;
  endTime?: string;
  breakStart?: string;
  breakEnd?: string;
}

// 班次項目介面 - 對應 API 中的 CalendarDay，添加組件需要的屬性
export interface ShiftItem extends CalendarDay {
  employeeId?: string;
  employeeName?: string;
}

// 從 API 資料轉換為組件所需格式的工具函數
export const convertApiDataToComponent = (apiData: ShiftDataResponse) => {
  // 合併 monthLeaves 和 shifts 陣列
  const allShiftDefinitions = [
    ...(apiData.shiftsDefinition.monthLeaves || []),
    ...(apiData.shiftsDefinition.shifts || [])
  ];
  
  return {
    employees: apiData.employees,
    shifts: allShiftDefinitions,
    // 將所有員工的 calendars 展平為 ShiftItem 陣列
    shiftItems: apiData.employees.flatMap((employee: ApiEmployee) => 
      employee.calendars.map((calendar: CalendarDay) => ({
        ...calendar,
        employeeId: employee.employeeNumber,
        employeeName: employee.chineseName
      }))
    )
  };
};

// 預設的班次類型 - 基於 API 資料結構
export const defaultShiftTypes: ShiftType[] = [
  {
    shiftIds: [],
    cycles: [],
    dayStartTime: "2010-10-09T16:00:00+00:00",
    shiftScheduleId: "early-shift",
    shiftScheduleName: "早班",
    colorCode: "#4F46E5",
    remnant: null,
    title: "早班",
    id: "early",
    color: "#4F46E5",
    startTime: "08:00",
    endTime: "16:00",
    breakStart: "12:00",
    breakEnd: "13:00"
  },
  {
    shiftIds: [],
    cycles: [],
    dayStartTime: "2010-10-09T16:00:00+00:00", 
    shiftScheduleId: "middle-shift",
    shiftScheduleName: "中班",
    colorCode: "#0EA5E9",
    remnant: null,
    title: "中班",
    id: "middle",
    color: "#0EA5E9",
    startTime: "16:00",
    endTime: "24:00",
    breakStart: "20:00",
    breakEnd: "21:00"
  },
  {
    shiftIds: [],
    cycles: [],
    dayStartTime: "2010-10-09T16:00:00+00:00",
    shiftScheduleId: "late-shift", 
    shiftScheduleName: "晚班",
    colorCode: "#10B981",
    remnant: null,
    title: "晚班",
    id: "late",
    color: "#10B981",
    startTime: "00:00",
    endTime: "08:00",
    breakStart: "04:00",
    breakEnd: "05:00"
  },
  {
    shiftIds: [],
    cycles: [],
    dayStartTime: "2010-10-09T16:00:00+00:00",
    shiftScheduleId: "rest-shift",
    shiftScheduleName: "休息",
    colorCode: "#F59E0B", 
    remnant: null,
    title: "休息",
    id: "rest",
    color: "#F59E0B",
    startTime: "",
    endTime: ""
  },
  {
    shiftIds: [],
    cycles: [],
    dayStartTime: "2010-10-09T16:00:00+00:00",
    shiftScheduleId: "00000000-0000-0000-0000-000000000003",
    shiftScheduleName: "例假日",
    colorCode: "#99999B",
    remnant: null,
    title: "例假日",
    id: "holiday",
    color: "#99999B", 
    startTime: "",
    endTime: ""
  }
];

// 預設員工資料 - 保持向下相容，但使用 API 結構
export const defaultEmployees: Employee[] = [
  {
    chineseName: "王小明",
    employeeNumber: "E0001",
    schedulingStatus: 1,
    totalMonthLeaveTime: 0,
    totalScheduleLeaveTime: 0,
    fatigueValue: 0,
    schedulingStatusRangeMap: {},
    empSchedulingStartDate: "0001-01-01T00:00:00+00:00",
    empSchedulingEndDate: "0001-01-01T00:00:00+00:00", 
    supervisorSchedulingStartDate: "0001-01-01T00:00:00+00:00",
    supervisorSchedulingEndDate: "0001-01-01T00:00:00+00:00",
    selectShiftSchedule: true,
    arrangeLeave: true,
    isShiftRules: true,
    adjustmentScheduleTime: true,
    advanceLeave: true,
    isEditable: true,
    isSupervisorEditable: true,
    calendars: [],
    // 統計欄位
    totalWorkTime: 0,
    totalLeaveTime: 0,
    totalOverTime: 0,
    totalTripMinutes: 0,
    monthLeaveDays: 0,
    monthLeaveDaysUsed: 0,
    // 向下相容屬性
    id: "E0001",
    name: "王小明"
  },
  {
    chineseName: "李大仁", 
    employeeNumber: "E0002",
    schedulingStatus: 1,
    totalMonthLeaveTime: 0,
    totalScheduleLeaveTime: 0,
    fatigueValue: 0,
    schedulingStatusRangeMap: {},
    empSchedulingStartDate: "0001-01-01T00:00:00+00:00",
    empSchedulingEndDate: "0001-01-01T00:00:00+00:00",
    supervisorSchedulingStartDate: "0001-01-01T00:00:00+00:00", 
    supervisorSchedulingEndDate: "0001-01-01T00:00:00+00:00",
    selectShiftSchedule: true,
    arrangeLeave: true,
    isShiftRules: true,
    adjustmentScheduleTime: true,
    advanceLeave: true,
    isEditable: true,
    isSupervisorEditable: true,
    calendars: [],
    // 統計欄位
    totalWorkTime: 0,
    totalLeaveTime: 0,
    totalOverTime: 0,
    totalTripMinutes: 0,
    monthLeaveDays: 0,
    monthLeaveDaysUsed: 0,
    // 向下相容屬性
    id: "E0002",
    name: "李大仁"
  }
];

// 工具函數：從 API 資料中提取員工資訊
export const extractEmployeesFromApi = (apiData: ShiftDataResponse): Employee[] => {
  return apiData.employees.map(emp => ({
    ...emp,
    id: emp.employeeNumber,
    name: emp.chineseName
  }));
};

// 工具函數：從 API 資料中提取班次定義
export const extractShiftTypesFromApi = (apiData: ShiftDataResponse): ShiftType[] => {
  // 合併 monthLeaves 和 shifts 陣列
  const allShiftDefinitions = [
    ...(apiData.shiftsDefinition.monthLeaves || []),
    ...(apiData.shiftsDefinition.shifts || [])
  ];
  
  return allShiftDefinitions.map(shift => ({
    ...shift,
    title: shift.shiftScheduleName,
    id: shift.shiftScheduleId,
    color: shift.colorCode
  }));
};

// 工具函數：格式化時間顯示
export const formatTimeFromISO = (isoTime: string | null): string => {
  if (!isoTime) return '';
  try {
    const date = new Date(isoTime);
    return date.toLocaleTimeString('zh-TW', { 
      hour: '2-digit', 
      minute: '2-digit', 
      hour12: false 
    });
  } catch {
    return '';
  }
};

// 工具函數：獲取班次顯示顏色
export const getShiftColor = (shiftSchedule: ShiftSchedule | null): string => {
  return shiftSchedule?.colorCode || '#99999B';
};

// 工具函數：獲取班次顯示名稱
export const getShiftTitle = (shiftSchedule: ShiftSchedule | null): string => {
  return shiftSchedule?.shiftScheduleName || '未排班';
}; 