import React from 'react';
import { Employee } from '../../../datas/api.types';
import { calculateTotalStats, calculateDailyAttendanceCount } from './shift-business-logic';

// 統計數字顯示組件
interface StatsNumberProps {
  value: number;
  className?: string;
}

const StatsNumber: React.FC<StatsNumberProps> = ({ value, className }) => (
  <div className={`font-medium text-sm ${className}`}>{value}</div>
);

// 統計標籤組件
interface StatsLabelProps {
  label: string;
}

const StatsLabel: React.FC<StatsLabelProps> = ({ label }) => <div className="text-xs">{label}</div>;

// 統計欄位組件
interface StatsColumnProps {
  label?: string;
  value: number;
  hasBorder?: boolean;
}

const StatsColumn: React.FC<StatsColumnProps> = ({ label, value, hasBorder = true }) => (
  <div
    className={`flex-1 ${hasBorder ? 'border-r border-gray-300' : ''} flex flex-col items-center justify-center`}
  >
    {label && <StatsLabel label={label} />}
    <StatsNumber value={value} />
  </div>
);

// 員工統計單元格元件
interface EmployeeStatsCellProps {
  employee: Employee;
}

export const EmployeeStatsCell: React.FC<EmployeeStatsCellProps> = ({ employee }) => {
  return (
    <div
      className="sticky left-[200px] z-[9999] min-w-[120px] border border-gray-300 bg-dark-50 flex"
      style={{ minHeight: '40px' }}
    >
      <StatsColumn value={employee.totalWorkTime} />
      <StatsColumn value={employee.totalLeaveTime} />
      <StatsColumn value={employee.totalOverTime} hasBorder={false} />
    </div>
  );
};

// 全部統計組件
interface TotalStatsProps {
  employees: Employee[];
}

export const TotalStats: React.FC<TotalStatsProps> = ({ employees }) => {
  const totalStats = calculateTotalStats(employees);

  return (
    <div className="sticky left-[200px] z-30 min-w-[120px] border border-gray-300 bg-dark-50">
      <div className="flex flex-1 h-full text-blue text-bold">
        <StatsColumn value={totalStats.totalWorkTime} />
        <StatsColumn value={totalStats.totalLeaveTime} />
        <StatsColumn value={totalStats.totalOverTime} hasBorder={false} />
      </div>
    </div>
  );
};

// 每日出勤統計組件
interface DailyAttendanceProps {
  date: string;
  employees: Employee[];
}

export const DailyAttendance: React.FC<DailyAttendanceProps> = ({ date, employees }) => {
  const totalCount = calculateDailyAttendanceCount(date, employees);

  return (
    <div className="min-w-[80px] flex-1 border border-gray-300 p-1 text-center flex items-center justify-center bg-dark-50">
      <StatsNumber value={totalCount} className="text-blue" />
    </div>
  );
};

// 統計標題組件
export const StatsHeader: React.FC = () => (
  <div className="sticky left-[200px] z-30 min-w-[120px] border border-t-0 border-gray-300 bg-dark-50 p-1 flex flex-col">
    <div className="text-xs font-bold mb-1 text-center">全部統計</div>
    <div className="flex flex-1 text-blue">
      <div
        className={`flex-1 flex flex-col items-center justify-center border-r border-gray-300 text-xs`}
      >
        上班
      </div>
      <div
        className={`flex-1 flex flex-col items-center justify-center border-r border-gray-300 text-xs`}
      >
        請假
      </div>
      <div className={`flex-1 flex flex-col items-center justify-center text-xs`}>排休</div>
    </div>
  </div>
);

// 統計標題行組件
export const StatsHeaderRow: React.FC<{ title: React.ReactNode }> = ({ title }) => (
  <div className="sticky left-0 z-30 flex justify-end items-center w-[200px] border border-gray-300 border-x-0 bg-dark-50 pl-2 py-1 text-white font-medium text-sm">
    {title}
  </div>
);
