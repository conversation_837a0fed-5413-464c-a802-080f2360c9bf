import React from 'react';
import { ShiftSchedule } from '../../../datas/api.types';
import { formatTimeFromISO, getShiftColor, getShiftTitle } from './shift-constants';

// 顏色工具函式
export function getColorWithOpacity(color: string, opacity: number = 0.3): string {
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  return color;
}

// 基本班次標籤組件
interface ShiftBadgeProps {
  children: React.ReactNode;
  color: string;
  className?: string;
}

export const ShiftBadge: React.FC<ShiftBadgeProps> = ({ children, color, className = '' }) => (
  <div
    className={`rounded px-1 py-0.5 flex justify-center items-center text-xs min-w-16 min-h-8 ${className}`}
  >
    <div className={color}>{children}</div>
  </div>
);

// 工作日班次組件
interface WorkDayShiftProps {
  shiftSchedule: ShiftSchedule;
}

export const WorkDayShift: React.FC<WorkDayShiftProps> = ({ shiftSchedule }) => {
  const color = getShiftColor(shiftSchedule);
  const title = getShiftTitle(shiftSchedule);

  const hasBreak =
    shiftSchedule.restTimeStart1 &&
    shiftSchedule.restTimeEnd1 &&
    shiftSchedule.workOnTime &&
    shiftSchedule.workOffTime;

  const baseClass =
    'rounded px-1 py-0.5 text-center text-[9px] text-black flex justify-start items-center gap-1';
  const backgroundColor = getColorWithOpacity(color, 0.2);

  if (hasBreak) {
    return (
      <React.Fragment>
        <div className={baseClass} style={{ backgroundColor }}>
          <div className="w-1 h-4 rounded-full" style={{ backgroundColor: color }} />
          <div className="flex flex-col items-center justify-center text-black text-[9px]">
            <div>{title}A</div>
            <div>
              {formatTimeFromISO(shiftSchedule.workOnTime)}-
              {formatTimeFromISO(shiftSchedule.restTimeStart1)}
            </div>
          </div>
        </div>

        <div className={baseClass} style={{ backgroundColor }}>
          <div className="w-1 h-4 rounded-full" style={{ backgroundColor: color }} />
          <div className="flex flex-col items-center justify-center text-black text-[9px]">
            <div>{title}B</div>
            <div>
              {formatTimeFromISO(shiftSchedule.restTimeEnd1)}-
              {formatTimeFromISO(shiftSchedule.workOffTime)}
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }

  return (
    <div className={baseClass} style={{ backgroundColor }}>
      <div className="w-1 h-5 rounded-full" style={{ backgroundColor: color }} />
      <div className="flex flex-col items-center justify-center text-black text-[9px]">
        <div>{title}</div>
        {shiftSchedule.workOnTime && shiftSchedule.workOffTime && (
          <div>
            {formatTimeFromISO(shiftSchedule.workOnTime)}-
            {formatTimeFromISO(shiftSchedule.workOffTime)}
          </div>
        )}
      </div>
    </div>
  );
};

// 補休標籤
export const DayOffBadge: React.FC = () => (
  <ShiftBadge color="text-blue-700" className="bg-blue-100 border border-blue-300">
    補休
  </ShiftBadge>
);

// 全月請假標籤
export const MonthLeaveBadge: React.FC = () => (
  <ShiftBadge color="text-purple-700" className="bg-purple-100 border border-purple-300">
    月休
  </ShiftBadge>
);

// 休息日標籤
export const RestDayBadge: React.FC = () => (
  <ShiftBadge color="text-gray-700" className="bg-gray-100 border border-gray-300">
    休息
  </ShiftBadge>
);

// 一般假期標籤
export const UsualHolidayBadge: React.FC = () => (
  <ShiftBadge color="text-green-700" className="bg-green-100 border border-green-300">
    例假日
  </ShiftBadge>
);

// 請假標籤
export const LeaveBadge: React.FC = () => (
  <ShiftBadge color="text-orange-700" className="bg-orange-100 border border-orange-300">
    請假
  </ShiftBadge>
);

// 出差標籤
export const TripBadge: React.FC = () => (
  <ShiftBadge color="text-indigo-700" className="bg-indigo-100 border border-indigo-300">
    出差
  </ShiftBadge>
);

// 支援部門標籤
export const SupportBadge: React.FC = () => (
  <ShiftBadge color="text-yellow-700" className="bg-yellow-100 border border-yellow-300">
    支援
  </ShiftBadge>
);

// 無排班標籤
export const NoScheduleBadge: React.FC = () => <ShiftBadge color="text-gray-500">{''}</ShiftBadge>;

// 節假日標籤
export const HolidayBadge: React.FC = () => (
  <ShiftBadge color="text-red-700" className="bg-red-100 border border-red-300">
    國定假日
  </ShiftBadge>
);

// 調班標記
export const AdjustmentMark: React.FC = () => <div className="w-full h-0.5 bg-blue-500 mt-1" />;

// 班次容器組件
interface ShiftContainerProps {
  children: React.ReactNode;
}

export const ShiftContainer: React.FC<ShiftContainerProps> = ({ children }) => (
  <div className="relative z-10 flex flex-col items-center gap-1 my-2">{children}</div>
);
