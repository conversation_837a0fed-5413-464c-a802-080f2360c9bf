import { X } from 'lucide-react';
import ShiftApp from './shift_setting/shift';

interface ShiftSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * 排班設定模態框組件
 * 包裝 shift.tsx 組件並提供模態框功能
 */
export function ShiftSettingsModal({ isOpen, onClose }: ShiftSettingsModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[70]">
      <div
        className="bg-white rounded-xl shadow-2xl w-full max-w-[95vw] max-h-[95vh] overflow-hidden transform transition-transform duration-300"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 模態框標題列 */}
        <div className="flex items-center justify-between p-4 border-b border-slate-200">
          <h2 className="text-xl font-bold text-slate-800">排班設定</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-100 rounded-lg transition-colors"
            aria-label="關閉排班設定"
          >
            <X className="size-5 text-slate-600" />
          </button>
        </div>

        {/* Shift 應用程式內容 */}
        <div className="overflow-auto max-h-[calc(95vh-80px)]">
          <ShiftApp />
        </div>
      </div>
    </div>
  );
}
