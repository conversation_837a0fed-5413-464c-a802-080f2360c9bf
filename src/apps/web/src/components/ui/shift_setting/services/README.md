# API 服務使用指南

## 概述

API 服務已從 `fetch` 重構為 `axios`，提供更好的錯誤處理、攔截器支援和 React Query 整合準備。

**最新更新**：新增員工 API 服務，支援取得目前在職員工清單。

## 核心功能

### ✅ 已實現功能

1. **統一錯誤處理**：所有 API 錯誤都會被統一處理和分類
2. **請求/響應攔截器**：自動日誌記錄和錯誤通知
3. **TypeScript 增強**：更嚴格的類型檢查和 API 響應類型
4. **React Query 準備**：支援 AbortSignal 和查詢鍵設計
5. **向後兼容**：所有現有調用方式保持不變
6. **員工 API 服務**：取得目前在職員工清單，支援多種篩選方式

### 🔄 遷移狀態

- ✅ 基礎 axios 實現
- ✅ 錯誤處理機制
- ✅ 攔截器配置
- ✅ TypeScript 類型更新
- ✅ 員工 API 服務實現
- 📝 React Query 整合（文件準備，未來實施）

## 使用方式

### 班表 API（原有功能）

```typescript
import { shiftApi, shiftApiService } from './services/api';

// 函數式調用（原有方式）
const result = await shiftApi.getRequirements('kitchen');

// 類別調用（原有方式）
const requirements = await shiftApiService.getRequirements('kitchen');
```

### 員工 API（新增功能）

```typescript
import { employeeApi, employeeApiService } from './services/api';

// 取得所有目前在職員工
const allEmployees = await employeeApi.getCurrentEmployees();

// 根據員工 ID 篩選
const specificEmployees = await employeeApi.getEmployeesByIds([
  '3fa85f64-5717-4562-b3fc-2c963f66afa6',
  '4gb96g75-6828-5673-c4gd-3d074g77bgb7'
]);

// 根據部門 ID 篩選
const deptEmployees = await employeeApi.getEmployeesByDepartmentIds([
  'dept-marketing-123',
  'dept-engineering-456'
]);

// 組合篩選
const filteredEmployees = await employeeApi.getCurrentEmployees({
  ids: ['emp-001', 'emp-002'],
  deptIds: ['dept-001']
});

// 驗證 ID 格式
const isValidEmployeeId = employeeApi.validateEmployeeId('3fa85f64-5717-4562-b3fc-2c963f66afa6');
const isValidDeptId = employeeApi.validateDepartmentId('dept-marketing-123');
```

### 新增功能

```typescript
// 支援請求取消（為 React Query 準備）
const controller = new AbortController();
const result = await shiftApiService.getRequirements('kitchen', controller.signal);
const employees = await employeeApiService.getCurrentEmployees({}, controller.signal);

// 更詳細的錯誤處理
if (result.error) {
  console.log('錯誤代碼:', result.error.code);
  console.log('錯誤訊息:', result.error.message);
  console.log('HTTP 狀態:', result.error.status);
}
```

## React Query 整合指南

### 📋 準備工作

以下資源已準備完成，可直接用於 React Query 整合：

1. **查詢鍵工廠**：`shiftQueryKeys`, `employeeQueryKeys`
2. **AbortSignal 支援**：所有 API 方法支援請求取消
3. **統一錯誤類型**：`ApiError` 介面
4. **響應類型**：包含時間戳的 `ApiResponse<T>`

### 🚀 React Query Hook 範例

#### 班表 API Hooks

```typescript
// hooks/useShiftRequirements.ts
import { useQuery } from '@tanstack/react-query';
import { shiftApiService, shiftQueryKeys } from '../services/api';

export function useShiftRequirements(unitId: string) {
  return useQuery({
    queryKey: shiftQueryKeys.requirements(unitId),
    queryFn: ({ signal }) => shiftApiService.getRequirements(unitId, signal),
    enabled: !!unitId && shiftApiService.validateUnitId(unitId),
    staleTime: 5 * 60 * 1000, // 5分鐘
    cacheTime: 10 * 60 * 1000, // 10分鐘
  });
}

// hooks/useShiftMutation.ts
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { shiftApiService, shiftQueryKeys } from '../services/api';

export function useSaveShiftRequirements(unitId: string) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ data }: { data: JsonData }) => 
      shiftApiService.saveRequirements(unitId, data),
    onSuccess: () => {
      // 失效相關查詢
      queryClient.invalidateQueries(shiftQueryKeys.requirements(unitId));
      queryClient.invalidateQueries(shiftQueryKeys.all);
    },
    onError: (error) => {
      console.error('儲存失敗:', error);
    },
  });
}
```

#### 員工 API Hooks

```typescript
// hooks/useEmployees.ts
import { useQuery } from '@tanstack/react-query';
import { employeeApiService, employeeQueryKeys } from '../services/api';

export function useCurrentEmployees(
  queryParams?: GetEmployeesQueryParams,
  options?: { enabled?: boolean }
) {
  return useQuery({
    queryKey: employeeQueryKeys.current(),
    queryFn: ({ signal }) => employeeApiService.getCurrentEmployees(queryParams, signal),
    enabled: options?.enabled ?? true,
    staleTime: 10 * 60 * 1000, // 10分鐘
    cacheTime: 15 * 60 * 1000, // 15分鐘
  });
}

export function useEmployeesByIds(employeeIds: string[]) {
  return useQuery({
    queryKey: employeeQueryKeys.byIds(employeeIds),
    queryFn: ({ signal }) => employeeApiService.getEmployeesByIds(employeeIds, signal),
    enabled: employeeIds.length > 0,
    staleTime: 10 * 60 * 1000,
    cacheTime: 15 * 60 * 1000,
  });
}

export function useEmployeesByDepartments(departmentIds: string[]) {
  return useQuery({
    queryKey: employeeQueryKeys.byDepartments(departmentIds),
    queryFn: ({ signal }) => employeeApiService.getEmployeesByDepartmentIds(departmentIds, signal),
    enabled: departmentIds.length > 0,
    staleTime: 10 * 60 * 1000,
    cacheTime: 15 * 60 * 1000,
  });
}
```

### 🎯 組件使用範例

#### 班表設定表單

```typescript
// components/ShiftSettingsForm.tsx
import { useShiftRequirements, useSaveShiftRequirements } from '../hooks';

function ShiftSettingsForm({ unitId }: { unitId: string }) {
  const { data, isLoading, error } = useShiftRequirements(unitId);
  const saveRequirements = useSaveShiftRequirements(unitId);

  const handleSave = (formData: JsonData) => {
    saveRequirements.mutate({ data: formData });
  };

  if (isLoading) return <div>載入中...</div>;
  if (error) return <div>錯誤：{error.message}</div>;

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      handleSave(/* 表單資料 */);
    }}>
      {/* 表單內容 */}
      <button 
        type="submit" 
        disabled={saveRequirements.isLoading}
      >
        {saveRequirements.isLoading ? '儲存中...' : '儲存'}
      </button>
    </form>
  );
}
```

#### 員工選擇器

```typescript
// components/EmployeeSelector.tsx
import { useCurrentEmployees, useEmployeesByDepartments } from '../hooks';

function EmployeeSelector({ 
  departmentIds, 
  onEmployeeSelect 
}: { 
  departmentIds: string[], 
  onEmployeeSelect: (employee: Employee) => void 
}) {
  const { data: employeesData, isLoading, error } = useEmployeesByDepartments(departmentIds);

  if (isLoading) return <div>載入員工資料中...</div>;
  if (error) return <div>載入失敗：{error.message}</div>;

  const employees = employeesData?.data?.data || [];

  return (
    <div>
      <h3>選擇員工</h3>
      {employees.map(employee => (
        <div key={employee.employeeId} className="employee-item">
          <button onClick={() => onEmployeeSelect(employee)}>
            {employee.employeeName} ({employee.employeeNumber})
          </button>
        </div>
      ))}
    </div>
  );
}
```

## 錯誤處理

### 錯誤類型

```typescript
interface ApiError {
  message: string;      // 人類可讀的錯誤訊息
  code?: string | number; // 錯誤代碼
  status?: number;      // HTTP 狀態碼
  details?: any;        // 原始錯誤詳情
}
```

### 常見錯誤代碼

| 代碼 | 說明 | 處理建議 |
|------|------|----------|
| `UNIT_ID_NOT_CONFIGURED` | 單位 ID 未設定 | 提示用戶設定單位 ID |
| `EMPLOYEE_FETCH_ERROR` | 員工資料取得失敗 | 檢查網路連線，提供重試 |
| `INVALID_EMPLOYEE_IDS` | 員工 ID 格式錯誤 | 驗證員工 ID 格式 |
| `INVALID_DEPARTMENT_IDS` | 部門 ID 格式錯誤 | 驗證部門 ID 格式 |
| `NETWORK_ERROR` | 網路連線錯誤 | 檢查網路狀態，提供重試 |
| `401` | 未授權 | 重新登入 |
| `403` | 權限不足 | 提示權限不足 |
| `404` | 資源不存在 | 檢查 API 端點或資源 ID |
| `429` | 請求過於頻繁 | 延遲重試 |

### 錯誤通知自訂

```typescript
// 在 api.ts 中更新 notifyError 函數
const notifyError = (error: ApiError) => {
  // 整合到專案的通知系統
  if (error.status === 401) {
    // 跳轉到登入頁面
    window.location.href = '/login';
  } else if (error.code === 'EMPLOYEE_FETCH_ERROR') {
    // 特定員工錯誤處理
    toast.error('無法載入員工資料，請稍後再試');
  } else {
    // 一般錯誤通知
    toast.error(error.message);
  }
};
```

## 攔截器客製化

```typescript
// 取得 axios 實例進行高級配置
const axiosInstance = apiClient.getAxiosInstance();

// 添加自訂攔截器
axiosInstance.interceptors.request.use(config => {
  // 添加認證 token
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

## 測試支援

```typescript
// 測試時可以 mock axios 實例
import { apiClient } from './services/api';

// 在測試中
const mockAxios = jest.spyOn(apiClient.getAxiosInstance(), 'request');
mockAxios.mockResolvedValue({ data: mockData });
```

## 效能優化建議

1. **查詢快取**：使用適當的 `staleTime` 和 `cacheTime`
2. **請求去重**：React Query 自動處理相同查詢的去重
3. **背景更新**：利用 `refetchOnWindowFocus` 保持數據新鮮度
4. **錯誤重試**：配置合適的重試策略
5. **員工資料快取**：員工資料變化較少，可設定較長的快取時間

## 下一步

當準備整合 React Query 時：

1. 安裝依賴：`npm install @tanstack/react-query`
2. 設置 QueryClient Provider
3. 實作查詢和變更 hooks
4. 更新組件使用新的 hooks
5. 移除舊的狀態管理邏輯

---

*此文件隨 API 服務更新而維護，最後更新：2024年* 