import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import {
  JsonData,
  ApiResponse,
  AxiosConfig,
  ApiError,
  Employee,
  GetEmployeesQueryParams,
} from '../types';
import { API_CONFIG, UNIT_ID_MAPPING } from '../constants';

// 錯誤通知機制（可根據專案需求替換為 toast 或其他通知系統）
const notifyError = (error: ApiError) => {
  console.error('API 錯誤:', error);
  // TODO: 整合到專案的通知系統 (toast, alert 等)
};

// 統一錯誤處理函數
const handleAxiosError = (error: AxiosError): ApiError => {
  if (error.response) {
    // 伺服器回應了錯誤狀態碼
    const status = error.response.status;
    const data = error.response.data as any;

    const statusMessages: Record<number, string> = {
      400: '請求參數錯誤',
      401: '未授權，請重新登入',
      403: '權限不足',
      404: '資源不存在',
      409: '資料衝突',
      422: '資料驗證失敗',
      429: '請求過於頻繁，請稍後再試',
      500: '伺服器內部錯誤',
      502: '網關錯誤',
      503: '服務暫時不可用',
      504: '請求超時',
    };

    return {
      message: data?.message || statusMessages[status] || `HTTP ${status} 錯誤`,
      code: status,
      status,
      details: data,
    };
  } else if (error.request) {
    // 請求已發出但沒有收到回應
    return {
      message: '網路連線錯誤，請檢查網路狀態',
      code: 'NETWORK_ERROR',
      details: error.message,
    };
  } else {
    // 請求配置發生錯誤
    return {
      message: error.message || '請求配置錯誤',
      code: 'REQUEST_ERROR',
      details: error.message,
    };
  }
};

// 設置攔截器的函數
const setupInterceptors = (axiosInstance: AxiosInstance): void => {
  // 請求攔截器
  axiosInstance.interceptors.request.use(
    (config) => {
      // 添加時間戳避免快取
      if (config.method === 'get') {
        config.params = {
          ...config.params,
          _t: Date.now(),
        };
      }

      console.log(`🚀 API 請求: ${config.method?.toUpperCase()} ${config.url}`);
      return config;
    },
    (error) => {
      console.error('❌ 請求配置錯誤:', error);
      return Promise.reject(error);
    }
  );

  // 響應攔截器
  axiosInstance.interceptors.response.use(
    (response) => {
      console.log(`✅ API 響應: ${response.status} ${response.config.url}`);
      return response;
    },
    (error: AxiosError) => {
      const apiError = handleAxiosError(error);
      notifyError(apiError);
      return Promise.reject(apiError);
    }
  );
};

// 創建 axios 實例的函數（單例模式）
let axiosInstance: AxiosInstance | null = null;

const createAxiosInstance = (
  baseUrl: string = API_CONFIG.BASE_URL,
  defaultConfig: Partial<AxiosConfig> = {}
): AxiosInstance => {
  if (!axiosInstance) {
    axiosInstance = axios.create({
      baseURL: baseUrl,
      timeout: 10000, // 10秒超時
      withCredentials: true, // 對應原本的 credentials: 'include'
      headers: {
        // ✅ 移除默認的 Content-Type，讓各個 HTTP 方法函數自行設置
        ...defaultConfig.headers,
      },
      ...defaultConfig,
    });

    setupInterceptors(axiosInstance);
  }

  return axiosInstance;
};

// 通用請求函數
const request = async <T>(
  endpoint: string,
  config: Partial<AxiosConfig> = {}
): Promise<ApiResponse<T>> => {
  try {
    const instance = createAxiosInstance();
    const response: AxiosResponse<{ data: T }> = await instance.request({
      url: endpoint,
      ...config,
    });

    return {
      data: response.data.data,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    if (error && typeof error === 'object' && 'message' in error) {
      const apiError = error as ApiError;
      return {
        data: null as any,
        error: apiError,
        timestamp: new Date().toISOString(),
      };
    }

    // 未知錯誤
    return {
      data: null as any,
      error: {
        message: '未知錯誤',
        code: 'UNKNOWN_ERROR',
        details: error,
      },
      timestamp: new Date().toISOString(),
    };
  }
};

// HTTP 方法函數
export const apiGet = async <T>(
  endpoint: string,
  config?: Partial<AxiosConfig>
): Promise<ApiResponse<T>> => {
  return request<T>(endpoint, { ...config, method: 'GET' });
};

export const apiPost = async <T>(
  endpoint: string,
  data?: any,
  config?: Partial<AxiosConfig>
): Promise<ApiResponse<T>> => {
  return request<T>(endpoint, {
    ...config,
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/json',
      ...config?.headers,
    },
  });
};

export const apiPut = async <T>(
  endpoint: string,
  data?: any,
  config?: Partial<AxiosConfig>
): Promise<ApiResponse<T>> => {
  // 自動檢測 FormData
  const isFormData = data instanceof FormData;

  return request<T>(endpoint, {
    ...config,
    method: 'PUT',
    data,
    headers: {
      ...(isFormData ? {} : { 'Content-Type': 'application/json' }),
      ...config?.headers,
    },
  });
};

export const apiDelete = async <T>(
  endpoint: string,
  config?: Partial<AxiosConfig>
): Promise<ApiResponse<T>> => {
  return request<T>(endpoint, { ...config, method: 'DELETE' });
};

export const apiUploadFile = async <T>(
  endpoint: string,
  file: File | Blob,
  fileName: string = 'file',
  config?: Partial<AxiosConfig>
): Promise<ApiResponse<T>> => {
  const formData = new FormData();
  formData.append('file', file, fileName);

  return request<T>(endpoint, {
    ...config,
    method: 'PUT',
    data: formData,
    headers: {
      // ✅ 讓 axios 自動設定 Content-Type 和 boundary
      // 不要手動設置 Content-Type，否則會缺少 boundary 參數
      ...config?.headers,
    },
  });
};

// 獲取 axios 實例的函數（用於高級用法）
export const getAxiosInstance = (): AxiosInstance => {
  return createAxiosInstance();
};

// ===== ApiClient 函數式接口（保持向後兼容）=====
export const apiClient = {
  get: apiGet,
  post: apiPost,
  put: apiPut,
  delete: apiDelete,
  uploadFile: apiUploadFile,
  getAxiosInstance,
};

// ===== 班表 API 服務函數 =====

// 獲取班表需求資料
export const getShiftRequirements = async (
  departmentId: string,
  signal?: AbortSignal
): Promise<ApiResponse<JsonData>> => {
  const endpoint = `${API_CONFIG.PROXY_ENDPOINT}${API_CONFIG.API_BFF_URL}/sections/requirements/${departmentId}`;
  return apiGet<JsonData>(endpoint, { signal });
};

// 儲存班表需求資料
export const saveShiftRequirements = async (
  data: JsonData,
  companyId: string,
  departmentId: string,
  signal?: AbortSignal
): Promise<ApiResponse<any>> => {
  if (!departmentId) {
    return {
      data: null as any,
      error: {
        message: `尚未設定 ${departmentId} 的單位 ID，無法儲存`,
        code: 'UNIT_ID_NOT_CONFIGURED',
      },
      timestamp: new Date().toISOString(),
    };
  }
  console.log('saveShiftRequirements_data', data);

  // 將 JSON 資料轉換為 Blob
  const jsonBlob = new Blob([JSON.stringify(data)], {
    type: 'application/json',
  });

  const endpoint = `${API_CONFIG.PROXY_ENDPOINT}${API_CONFIG.API_BFF_URL}/sections/requirements/upload/${companyId}/${departmentId}/file`;
  return apiUploadFile(endpoint, jsonBlob, 'shift-settings.json', { signal });
};

// 驗證單位 ID 是否設定
export const validateShiftUnitId = (unitId: string): boolean => {
  const sectionId = UNIT_ID_MAPPING[unitId];
  return !(!sectionId || sectionId.startsWith('YOUR'));
};

// ===== ShiftApiService 函數式接口（保持向後兼容）=====
export const shiftApiService = {
  getRequirements: getShiftRequirements,
  saveRequirements: saveShiftRequirements,
  validateUnitId: validateShiftUnitId,
};

// ===== 員工 API 服務函數 =====

// 建構查詢參數字串
const buildQueryString = (params: GetEmployeesQueryParams): string => {
  const queryParts: string[] = [];

  if (params.ids && params.ids.length > 0) {
    params.ids.forEach((id) => {
      queryParts.push(`ids=${encodeURIComponent(id)}`);
    });
  }

  if (params.deptIds && params.deptIds.length > 0) {
    params.deptIds.forEach((deptId) => {
      queryParts.push(`deptIds=${encodeURIComponent(deptId)}`);
    });
  }

  return queryParts.length > 0 ? `?${queryParts.join('&')}` : '';
};

// 取得目前在職員工清單
export const getCurrentEmployees = async (
  queryParams: GetEmployeesQueryParams = {},
  signal?: AbortSignal
): Promise<ApiResponse<Employee[]>> => {
  const queryString = buildQueryString(queryParams);
  const endpoint = `${API_CONFIG.PROXY_ENDPOINT}${API_CONFIG.API_BFF_URL}/employees/now/serving${queryString}`;

  console.log('🔍 取得在職員工清單:', endpoint);

  try {
    const response = await apiGet<Employee[]>(endpoint, { signal });
    console.log('getCurrentEmployees_response', response);

    if (response.error) {
      console.error('❌ 取得員工清單失敗:', response.error);
      return response;
    }

    const employeeCount = response.data?.length || 0;
    console.log(`✅ 成功取得 ${employeeCount} 名員工資料`);

    return response;
  } catch (error) {
    console.error('❌ 取得員工清單發生錯誤:', error);

    return {
      data: null as any,
      error: {
        message: '取得員工清單失敗',
        code: 'EMPLOYEE_FETCH_ERROR',
        details: error,
      },
      timestamp: new Date().toISOString(),
    };
  }
};

// 根據員工ID取得員工資料
export const getEmployeesByIds = async (
  employeeIds: string[],
  signal?: AbortSignal
): Promise<ApiResponse<Employee[]>> => {
  if (!employeeIds || employeeIds.length === 0) {
    return {
      data: null as any,
      error: {
        message: '員工ID不能為空',
        code: 'INVALID_EMPLOYEE_IDS',
      },
      timestamp: new Date().toISOString(),
    };
  }

  return getCurrentEmployees({ ids: employeeIds }, signal);
};

// 根據部門ID取得員工資料
export const getEmployeesByDepartmentIds = async (
  departmentIds: string[],
  signal?: AbortSignal
): Promise<ApiResponse<Employee[]>> => {
  if (!departmentIds || departmentIds.length === 0) {
    return {
      data: null as any,
      error: {
        message: '部門ID不能為空',
        code: 'INVALID_DEPARTMENT_IDS',
      },
      timestamp: new Date().toISOString(),
    };
  }

  return getCurrentEmployees({ deptIds: departmentIds }, signal);
};

// 驗證員工ID格式（假設使用GUID/UUID格式）
export const validateEmployeeId = (employeeId: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(employeeId);
};

// 驗證部門ID格式
export const validateDepartmentId = (departmentId: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(departmentId);
};

// ===== EmployeeApiService 函數式接口 =====
export const employeeApiService = {
  getCurrentEmployees,
  getEmployeesByIds,
  getEmployeesByDepartmentIds,
  validateEmployeeId,
  validateDepartmentId,
};

// 為了向後兼容，提供原有的函數式 API
export const shiftApi = {
  getRequirements: (departmentId: string) => getShiftRequirements(departmentId),
  saveRequirements: (data: JsonData, companyId: string, departmentId: string) =>
    saveShiftRequirements(data, companyId, departmentId),
  validateUnitId: validateShiftUnitId,
};

export const employeeApi = {
  getCurrentEmployees: (queryParams?: GetEmployeesQueryParams) => getCurrentEmployees(queryParams),
  getEmployeesByIds: (employeeIds: string[]) => getEmployeesByIds(employeeIds),
  getEmployeesByDepartmentIds: (departmentIds: string[]) =>
    getEmployeesByDepartmentIds(departmentIds),
  validateEmployeeId,
  validateDepartmentId,
};

// React Query 查詢鍵工廠
export const employeeQueryKeys = {
  all: ['employee'] as const,
  current: () => ['employee', 'current'] as const,
  byIds: (ids: string[]) => ['employee', 'byIds', ids] as const,
  byDepartments: (deptIds: string[]) => ['employee', 'byDepartments', deptIds] as const,
} as const;

// React Query 查詢鍵工廠 (未來使用)
export const shiftQueryKeys = {
  all: ['shift'] as const,
  requirements: (unitId: string) => ['shift', 'requirements', unitId] as const,
  validation: (unitId: string) => ['shift', 'validation', unitId] as const,
} as const;

// 未來遷移到 axios + react-query 時的接口預留（已更新）
export interface ShiftApiInterface {
  getRequirements(unitId: string, signal?: AbortSignal): Promise<ApiResponse<JsonData>>;
  saveRequirements(
    unitId: string,
    data: JsonData,
    organizationId?: string,
    signal?: AbortSignal
  ): Promise<ApiResponse<any>>;
  validateUnitId(unitId: string): boolean;
}

// 預設匯出（向後兼容）
export default shiftApiService;
