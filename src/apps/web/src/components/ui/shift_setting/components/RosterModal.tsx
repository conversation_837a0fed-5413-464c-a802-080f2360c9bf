import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { RosterModalProps, StaffItemProps, Employee } from '../types';
import { employeeApi } from '../services/api';

const RosterModal: React.FC<RosterModalProps> = ({
  isOpen,
  onClose,
  role,
  departmentId,
  onSave,
  staffAssignments,
}) => {
  const [assignedIds, setAssignedIds] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');

  // 新增：員工資料相關 state
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 載入員工資料
  const loadEmployees = useCallback(async () => {
    if (!departmentId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await employeeApi.getEmployeesByDepartmentIds([departmentId]);

      if (response.error) {
        setError(response.error.message);
        setEmployees([]);
      } else {
        setEmployees(response.data || []);
      }
    } catch (err) {
      setError('載入員工資料失敗');
      setEmployees([]);
    } finally {
      setIsLoading(false);
    }
  }, [departmentId]);

  // 當 Modal 開啟時載入員工資料
  useEffect(() => {
    if (isOpen && departmentId) {
      loadEmployees();
    }
  }, [isOpen, departmentId, loadEmployees]);

  // 當 Modal 關閉時清除資料
  useEffect(() => {
    if (!isOpen) {
      setEmployees([]);
      setError(null);
      setSearchTerm('');
    }
  }, [isOpen]);

  // 設定已指派的員工ID
  useEffect(() => {
    if (role) {
      setAssignedIds(new Set(role.roster || []));
    }
  }, [role]);

  // 更新搜尋邏輯以使用新的 Employee 資料結構
  const filteredAndSortedEmployees = useMemo(() => {
    if (!employees.length) return [];

    return employees
      .filter((employee) => {
        if (!searchTerm) return true;

        const term = searchTerm.toLowerCase();
        const nameMatch = employee.employeeName.toLowerCase().includes(term);
        const numberMatch = employee.employeeNumber.toLowerCase().includes(term);

        // 檢查角色指派匹配
        const assignedRoles = staffAssignments[employee.employeeId] || [];
        const roleMatch = assignedRoles.some((roleName) => roleName.toLowerCase().includes(term));

        return nameMatch || numberMatch || roleMatch;
      })
      .sort((a, b) => a.employeeName.localeCompare(b.employeeName));
  }, [employees, searchTerm, staffAssignments]);

  // 檢查員工是否已被其他群組指派
  const getEmployeeAssignmentStatus = useCallback(
    (employeeId: string) => {
      if (!role) return { hasOtherAssignment: false, otherRoles: [] };

      const assignedRoles = staffAssignments[employeeId] || [];
      const otherRoles = assignedRoles.filter((roleName) => roleName !== role.name);
      return {
        hasOtherAssignment: otherRoles.length > 0,
        otherRoles: otherRoles,
      };
    },
    [staffAssignments, role]
  );

  // 可選擇的員工：未被指派到當前群組 且 未被其他群組指派
  const availableEmployees = filteredAndSortedEmployees.filter(
    (employee) =>
      !assignedIds.has(employee.employeeId) &&
      !getEmployeeAssignmentStatus(employee.employeeId).hasOtherAssignment
  );

  // 已指派到當前群組的員工
  const assignedEmployees = filteredAndSortedEmployees.filter((employee) =>
    assignedIds.has(employee.employeeId)
  );

  // 已被其他群組指派的員工（僅供顯示）
  const unavailableEmployees = filteredAndSortedEmployees.filter(
    (employee) =>
      !assignedIds.has(employee.employeeId) &&
      getEmployeeAssignmentStatus(employee.employeeId).hasOtherAssignment
  );

  if (!isOpen || !role) return null;

  const toggleEmployee = (employeeId: string) => {
    setAssignedIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(employeeId)) {
        newSet.delete(employeeId);
      } else {
        newSet.add(employeeId);
      }
      return newSet;
    });
  };

  const handleSave = () => {
    onSave(role.id, Array.from(assignedIds));
    onClose();
  };

  const handleClose = () => {
    onClose();
  };

  // 更新 StaffItem 組件以使用新的 Employee 資料結構
  const StaffItem: React.FC<StaffItemProps> = ({ staff, isAssigned, assignments }) => {
    const assignedRoles = (assignments[staff.employeeId] || []).filter(
      (roleName) => roleName !== role.name
    );

    // 只有「未指派到當前群組」且「已被其他群組指派」的員工才不可用
    const isUnavailable = !isAssigned && assignedRoles.length > 0;

    return (
      <div
        onClick={() => !isUnavailable && toggleEmployee(staff.employeeId)}
        className={`flex items-center justify-between p-2 rounded-md transition ${
          isUnavailable
            ? 'bg-gray-100 cursor-not-allowed opacity-60'
            : isAssigned
              ? 'bg-indigo-100 cursor-pointer hover:bg-indigo-200'
              : 'bg-white cursor-pointer hover:bg-slate-100'
        }`}
      >
        <div className="flex flex-col text-left gap-1">
          <div className="flex items-center gap-2 flex-wrap">
            <span className={`font-semibold ${isUnavailable ? 'text-gray-500' : ''}`}>
              {staff.employeeName}
            </span>
            <span className={`text-xs ${isUnavailable ? 'text-gray-400' : 'text-slate-500'}`}>
              ({staff.employeeNumber})
            </span>
            {assignedRoles.map((roleName) => (
              <span
                key={roleName}
                className={`text-xs font-semibold px-1.5 py-0.5 rounded-full ${
                  isUnavailable ? 'bg-gray-300 text-gray-600' : 'bg-slate-200 text-slate-700'
                }`}
              >
                {roleName}
              </span>
            ))}
          </div>
          {isUnavailable && <span className="text-xs text-gray-500">已被其他群組指派</span>}
        </div>
        {!isUnavailable && (
          <span className="text-xl font-bold text-indigo-500">{isAssigned ? '−' : '+'}</span>
        )}
      </div>
    );
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={handleClose}
    >
      <div
        className="bg-white rounded-xl shadow-2xl w-full max-w-3xl transform transition-transform duration-300"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="px-6 py-4 border-b border-slate-200">
          <h3 className="text-lg font-bold text-slate-800">指派成員到 "{role.name}"</h3>
          <div className="mt-4">
            <input
              type="text"
              placeholder="搜尋姓名、工號、角色..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="p-6 space-y-6" style={{ height: '500px', maxHeight: '60vh' }}>
          {/* 載入狀態 */}
          {isLoading && (
            <div className="flex items-center justify-center h-full">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 border-2 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-slate-600">載入員工資料中...</span>
              </div>
            </div>
          )}

          {/* 錯誤狀態 */}
          {error && (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="text-red-500 mb-2">⚠️ {error}</div>
                <button
                  onClick={loadEmployees}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition"
                >
                  重試
                </button>
              </div>
            </div>
          )}

          {/* 員工清單 */}
          {!isLoading && !error && (
            <div className="flex flex-col h-full space-y-4">
              {/* 上方：可選擇成員 & 已指派成員 橫向排列 */}
              <div className="flex-1 flex flex-row gap-4 min-h-0">
                <div className="flex-1 flex flex-col min-h-0">
                  <h4 className="font-semibold text-slate-600 mb-2">
                    可選擇成員 ({availableEmployees.length})
                  </h4>
                  <div className="flex-grow overflow-y-auto border border-slate-200 rounded-lg p-2 space-y-2 bg-slate-50">
                    {availableEmployees.length === 0 ? (
                      <div className="text-center text-slate-500 py-4">
                        {searchTerm ? '沒有符合搜尋條件的可選擇員工' : '沒有可選擇的員工'}
                      </div>
                    ) : (
                      availableEmployees.map((employee) => (
                        <StaffItem
                          key={employee.employeeId}
                          staff={employee}
                          isAssigned={false}
                          assignments={staffAssignments}
                        />
                      ))
                    )}
                  </div>
                </div>

                <div className="flex-1 flex flex-col min-h-0">
                  <h4 className="font-semibold text-slate-600 mb-2">
                    已指派成員 ({assignedEmployees.length})
                  </h4>
                  <div className="flex-grow overflow-y-auto border border-slate-200 rounded-lg p-2 space-y-2 bg-slate-50">
                    {assignedEmployees.length === 0 ? (
                      <div className="text-center text-slate-500 py-4">尚未指派任何成員</div>
                    ) : (
                      assignedEmployees.map((employee) => (
                        <StaffItem
                          key={employee.employeeId}
                          staff={employee}
                          isAssigned={true}
                          assignments={staffAssignments}
                        />
                      ))
                    )}
                  </div>
                </div>
              </div>

              {/* 下方：已被其他群組指派 獨立區塊 */}
              {unavailableEmployees.length > 0 && (
                <div className="max-h-[150px] flex flex-col">
                  <h4 className="font-semibold text-gray-500 mb-2">
                    已被其他群組指派 ({unavailableEmployees.length})
                  </h4>
                  <div className="flex-grow overflow-y-auto border border-gray-200 rounded-lg p-2 space-y-2 bg-gray-50">
                    {unavailableEmployees.map((employee) => (
                      <StaffItem
                        key={employee.employeeId}
                        staff={employee}
                        isAssigned={false}
                        assignments={staffAssignments}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="px-6 py-4 bg-slate-50 rounded-b-xl flex justify-end gap-3">
          <button
            onClick={handleClose}
            className="px-5 py-2 bg-white border border-slate-300 text-slate-700 font-semibold rounded-lg hover:bg-slate-100"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            disabled={isLoading}
            className="px-5 py-2 bg-indigo-600 text-white font-semibold rounded-lg shadow-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            確認儲存
          </button>
        </div>
      </div>
    </div>
  );
};

export default RosterModal;
