import React from 'react';
import { LoadingOverlayProps } from '../types';
import { ICONS } from '../constants';

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ text }) => (
  <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-xl z-50">
    <div className="text-center">
      {ICONS.bigSpinner}
      <p className="mt-2 text-slate-600 font-medium">{text}</p>
    </div>
  </div>
);

export default LoadingOverlay;
