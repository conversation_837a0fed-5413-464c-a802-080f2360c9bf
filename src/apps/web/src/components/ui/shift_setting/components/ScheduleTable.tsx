import React, { useMemo } from 'react';
import { ScheduleTableProps } from '../types';
import { sortTimeslots } from '../utils/helpers';

const ScheduleTable: React.FC<ScheduleTableProps> = React.memo(
  ({ schedule, onUpdate, onShowRosterModal, onShowActionModal }) => {
    const sortedTimeslots = useMemo(
      () => sortTimeslots(schedule.timeslots || []),
      [schedule.timeslots]
    );

    const handleInputChange = (
      roleId: string,
      field: 'roleName' | 'remarks' | 'headcount',
      value: string | number,
      timeslot: string | null = null
    ) => {
      onUpdate(roleId, field, value, timeslot);
    };

    return (
      <div
        className="table-container w-full overflow-auto rounded-lg border border-slate-200"
        style={{ maxHeight: '65vh' }}
      >
        <table
          className="w-full text-sm text-slate-700 relative"
          style={{ minWidth: '1200px', borderCollapse: 'separate', borderSpacing: 0 }}
        >
          <thead className="sticky top-0 z-20">
            <tr>
              <th className="sticky left-0 top-0 z-30 p-2 font-semibold text-slate-600 bg-slate-200 border-b border-r border-slate-300">
                群組/時間
              </th>
              {sortedTimeslots.map((slot) => (
                <th
                  key={slot}
                  className="p-2 font-semibold text-slate-600 bg-slate-50 border-b border-slate-300"
                >
                  {slot}
                  <button
                    onClick={() => onShowActionModal({ type: 'delete-timeslot', data: slot })}
                    className="delete-timeslot-btn text-slate-400 hover:text-red-500 ml-1"
                  >
                    ×
                  </button>
                </th>
              ))}
              <th className="p-2 font-semibold text-slate-600 bg-slate-50 border-b border-slate-300">
                指派名單
              </th>
              <th className="p-2 font-semibold text-slate-600 bg-slate-50 border-b border-slate-300">
                備註
              </th>
            </tr>
          </thead>
          <tbody>
            {(schedule.roles || []).map((role) => (
              <tr key={role.id} data-role-id={role.id} className="group hover:bg-slate-50">
                <td className="sticky left-0 z-10 p-0 bg-white group-hover:bg-slate-50 border-b border-r border-slate-200">
                  <div className="flex items-center gap-2 p-2">
                    <input
                      type="text"
                      value={role.name}
                      onChange={(e) => handleInputChange(role.id, 'roleName', e.target.value)}
                      className="role-name-input w-24 p-1 rounded border border-transparent hover:border-slate-300 focus:border-indigo-500 focus:outline-none bg-transparent"
                      autoComplete="off"
                    />
                    <button
                      onClick={() => onShowActionModal({ type: 'delete-role', data: role })}
                      className="text-slate-400 hover:text-red-500"
                    >
                      ×
                    </button>
                  </div>
                </td>
                {sortedTimeslots.map((slot) => {
                  const count = schedule.headcount?.[role.id]?.[slot] || 0;
                  return (
                    <td key={slot} className="p-1 border-b border-slate-200">
                      <input
                        type="number"
                        min="0"
                        value={count}
                        onChange={(e) =>
                          handleInputChange(
                            role.id,
                            'headcount',
                            parseInt(e.target.value) || 0,
                            slot
                          )
                        }
                        className="headcount-input w-[60px] text-center rounded-md border border-slate-300 p-1 focus:border-indigo-500 focus:ring-indigo-500"
                      />
                    </td>
                  );
                })}
                <td className="p-2 border-b border-slate-200">
                  <div className="flex items-center justify-center gap-2">
                    <span className="roster-text truncate max-w-xs" title={role.rosterText}>
                      {role.rosterText || '未指派'}
                    </span>
                    <button
                      onClick={() => onShowRosterModal(role)}
                      className="text-indigo-600 hover:text-indigo-800 font-bold"
                    >
                      編輯
                    </button>
                  </div>
                </td>
                <td className="p-1 border-b border-slate-200">
                  <input
                    type="text"
                    value={role.remarks || ''}
                    onChange={(e) => handleInputChange(role.id, 'remarks', e.target.value)}
                    className="remarks-input w-48 p-1 rounded border border-transparent hover:border-slate-300 focus:border-indigo-500 focus:outline-none bg-transparent"
                    autoComplete="off"
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  }
);

ScheduleTable.displayName = 'ScheduleTable';

export default ScheduleTable;
