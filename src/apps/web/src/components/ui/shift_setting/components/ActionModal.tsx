import { useState, useEffect } from 'react';
import { ActionModalProps } from '../types';

const ActionModal: React.FC<ActionModalProps> = ({ config, onConfirm, onClose }) => {
  const [startTime, setStartTime] = useState('10:00');
  const [endTime, setEndTime] = useState('11:00');

  useEffect(() => {
    if (!config || !('mode' in config) || config.mode !== 'time-range') return;

    const data = config.data as { start: string; end: string } | undefined;
    setStartTime(data?.start || '10:00');
    setEndTime(data?.end || '11:00');
  }, [config]);

  if (!config) return null;

  const handleConfirm = () => {
    if (!('type' in config)) {
      config.onConfirm();
      return;
    }

    let result: any;
    switch (config.type) {
      case 'add-timeslot':
        result = { start: startTime, end: endTime };
        break;
      default:
        result = config.data;
    }
    onConfirm(config.type, result);
  };

  const { title, message } = config;
  const mode = 'mode' in config ? config.mode : 'confirm';

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[60]"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-xl shadow-2xl w-full max-w-md"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6">
          <h3 className="text-lg font-bold text-slate-800 mb-4">{title}</h3>
          {message && <p className="text-sm text-slate-600 mb-4">{message}</p>}
          {mode === 'time-range' && (
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-slate-700">開始時間</label>
                <input
                  type="time"
                  step="3600"
                  value={startTime}
                  onChange={(e) => setStartTime(e.target.value)}
                  className="mt-1 w-full form-input"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700">結束時間</label>
                <input
                  type="time"
                  step="3600"
                  value={endTime}
                  onChange={(e) => setEndTime(e.target.value)}
                  className="mt-1 w-full form-input"
                />
              </div>
            </div>
          )}
        </div>
        <div className="px-6 py-4 bg-slate-50 rounded-b-xl flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-5 py-2 bg-white border border-slate-300 text-slate-700 font-semibold rounded-lg hover:bg-slate-100"
          >
            取消
          </button>
          <button
            onClick={handleConfirm}
            className="px-5 py-2 bg-indigo-600 text-white font-semibold rounded-lg shadow-md hover:bg-indigo-700"
          >
            確認
          </button>
        </div>
      </div>
    </div>
  );
};

export default ActionModal;
