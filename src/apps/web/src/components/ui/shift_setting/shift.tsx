import { FC } from 'react';

// 引入重構後的模組
import { LoadingOverlay, ScheduleTable, RosterModal, ActionModal } from './components';
import { useShift } from './hooks/useShift';
import { useParentData } from '@/providers/ParentData';

const ShiftApp: FC = () => {
  const { processedData } = useParentData();

  const {
    // 狀態
    currentDepartmentId,
    isLoading,
    loadingText,
    saveStatus,
    isRosterModalOpen,
    editingRole,
    actionModalConfig,

    // 計算屬性
    scheduleWithRosterText,
    staffAssignments,

    // 動作
    setCurrentDepartmentId,
    handleSaveSettings,
    handleScheduleUpdate,
    handleAddRole,

    // Modal 相關
    openRosterModal,
    closeRosterModal,
    showActionModal,
    closeActionModal,
    handleActionConfirm,
  } = useShift();

  return (
    <div className="bg-slate-50 min-h-screen">
      {/* 狀態提示 */}
      <div
        className={`fixed top-5 right-5 z-[100] transition-all duration-300 ${
          saveStatus.visible
            ? 'opacity-100 translate-y-0'
            : 'opacity-0 -translate-y-5 pointer-events-none'
        }`}
      >
        <div
          className={`px-4 py-2 rounded-lg shadow-lg text-white font-semibold ${
            saveStatus.isError ? 'bg-red-600' : 'bg-slate-800'
          }`}
        >
          {saveStatus.msg}
        </div>
      </div>

      {/* 主內容 */}
      <div className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto bg-white rounded-xl shadow-lg p-5 sm:p-8 relative">
          {/* 載入遮罩 */}
          {isLoading && <LoadingOverlay text={loadingText} />}

          {/* 頁面標題和控制區 */}
          <header className="flex flex-wrap items-center justify-between mb-6 gap-4">
            <div className="flex items-center gap-4 flex-wrap">
              <h1 className="text-2xl font-bold text-slate-800">人力需求規則設定</h1>
              <select
                value={currentDepartmentId}
                onChange={(e) => {
                  setCurrentDepartmentId(e.target.value);
                }}
                className="form-select"
              >
                {processedData?.departments?.map((department) => (
                  <option key={department.departmentId} value={department.departmentId}>
                    {department.deptCName}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleSaveSettings}
                className="px-4 py-2 bg-orange-600 text-white font-semibold rounded-lg shadow-md hover:bg-orange-700"
              >
                儲存設定
              </button>
            </div>
          </header>

          {/* 排班表格 */}
          {scheduleWithRosterText ? (
            <ScheduleTable
              schedule={scheduleWithRosterText}
              onUpdate={handleScheduleUpdate}
              onShowRosterModal={openRosterModal}
              onShowActionModal={showActionModal}
            />
          ) : (
            <div className="text-center py-16 text-slate-500">正在載入設定，請稍候。</div>
          )}

          {/* 操作按鈕 */}
          <div className="mt-4 flex justify-start items-center sm:flex-row gap-4">
            <button
              onClick={handleAddRole}
              className="px-4 py-2 bg-slate-100 text-slate-800 font-medium rounded-md hover:bg-slate-200"
              disabled={!scheduleWithRosterText}
            >
              + 新增群組
            </button>
            <button
              onClick={() => showActionModal({ type: 'add-timeslot' })}
              className="px-4 py-2 bg-slate-100 text-slate-800 font-medium rounded-md hover:bg-slate-200"
              disabled={!scheduleWithRosterText}
            >
              + 新增時間段
            </button>
          </div>
        </div>
      </div>

      {/* 人員指派 Modal */}
      <RosterModal
        isOpen={isRosterModalOpen}
        onClose={closeRosterModal}
        role={editingRole}
        departmentId={currentDepartmentId}
        staffAssignments={staffAssignments}
        onSave={(roleId, roster) => handleScheduleUpdate(roleId, 'roster', roster)}
      />

      {/* 動作確認 Modal */}
      <ActionModal
        config={actionModalConfig}
        onConfirm={handleActionConfirm}
        onClose={closeActionModal}
      />
    </div>
  );
};

export default ShiftApp;
