import { JSX } from 'react';
import { JsonData, Unit } from './types';
import { v4 as uuidv4 } from 'uuid';

// --- API 設定 (開發環境使用 Vite 代理) ---
export const API_CONFIG = {
  BASE_URL: '', // 開發環境使用相對路徑，由 Vite 代理處理
  API_BFF_URL: import.meta.env.VITE_API_BFF_URL,
  PROXY_ENDPOINT: import.meta.env.VITE_PROXY_ENDPOINT, // 通過代理訪問
};

// 將前端的 unit ID 映射到後端的 section ID
export const UNIT_ID_MAPPING: { [key: string]: string } = {
  kitchen: 'e36bcd83-5340-4ba9-a940-f2890eb0af92',
  front_house: 'YOUR_FRONT_HOUSE_UNIT_ID_HERE', // **待填**：請填入「外場」對應的 ID
};

// --- 靜態資料與常數 (來自使用者提供的資料) ---
export const USER_PROVIDED_JSON: JsonData = {
  time_slots: [
    ['08:00', '09:00'],
    ['09:00', '10:00'],
    ['10:00', '11:00'],
    ['11:00', '12:00'],
    ['12:00', '13:00'],
    ['13:00', '14:00'],
    ['14:00', '15:00'],
    ['15:00', '16:00'],
    ['16:00', '17:00'],
    ['17:00', '18:00'],
    ['18:00', '19:00'],
    ['19:00', '20:00'],
    ['20:00', '21:00'],
    ['21:00', '22:00'],
    ['22:00', '23:00'],
    ['23:00', '00:00'],
  ],
  group_infos: [
    {
      group_id: uuidv4(),
      group_name: '主管',
      requirement: [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0],
      workers: [],
      note: '',
    },
    {
      group_id: uuidv4(),
      group_name: '廚房',
      requirement: [2, 2, 2, 3, 3, 3, 3, 2, 2, 1, 1, 1, 1, 1, 1, 0],
      workers: [],
      note: '',
    },
  ],
};

export const MOCK_DATA: { units: Unit[] } = {
  units: [
    { id: 'kitchen', name: '內場' },
    { id: 'front_house', name: '外場' },
  ],
};

export const START_OF_DAY = '06:00';

export const ICONS: { [key: string]: JSX.Element } = {
  delete: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M3 6h18" />
      <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" />
      <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
    </svg>
  ),
  bigSpinner: (
    <svg
      className="animate-spin h-10 w-10 text-blue-600 mx-auto"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      ></circle>
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
  ),
};
