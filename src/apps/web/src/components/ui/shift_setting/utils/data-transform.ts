import {
  JsonData,
  Schedule,
  StaffData,
  Role,
  Headcount,
  StaffMember,
  GroupInfo,
  Worker,
  Employee,
} from '../types';
import { v4 as uuidv4 } from 'uuid';

// 將後端 JSON 資料轉換為前端狀態
export const transformJsonToState = (
  jsonData: JsonData,
  unitId: string
): { schedule: Schedule; staff: StaffData } => {
  console.log('transformJsonToState - jsonData:', jsonData);

  if (!jsonData || !jsonData.time_slots || !jsonData.group_infos) {
    console.warn('Loaded JSON data is invalid or empty. Returning empty schedule.');
    return {
      schedule: { roles: [], timeslots: [], headcount: {} },
      staff: {},
    };
  }

  const newTimeslots = jsonData.time_slots.map((ts) => ts.join('-'));
  const newRoles: Role[] = [];
  const newHeadcount: Headcount = {};
  const allStaffMap = new Map<string, StaffMember>();
  let staffCounter = 0;

  // 轉換群組資訊
  jsonData.group_infos.forEach((group) => {
    const roleId = group.group_id || `role_${Date.now()}_${staffCounter++}`;
    const roster: string[] = [];

    // 處理員工資料
    (group.workers || []).forEach((worker) => {
      if (worker.employee_id && !allStaffMap.has(worker.employee_id)) {
        allStaffMap.set(worker.employee_id, {
          id: worker.employee_id,
          name: worker.employee_name,
          employeeIds: worker.employee_id,
        });
      }
      if (worker.employee_id) roster.push(worker.employee_id);
    });

    // 建立角色
    newRoles.push({
      id: roleId,
      name: group.group_name,
      roster: roster,
      remarks: group.note || '',
    });

    // 建立人力需求
    newHeadcount[roleId] = {};
    newTimeslots.forEach((slot, index) => {
      newHeadcount[roleId][slot] = group.requirement[index] || 0;
    });
  });

  const newStaffForUnit = Array.from(allStaffMap.values());

  return {
    schedule: {
      roles: newRoles,
      timeslots: newTimeslots,
      headcount: newHeadcount,
    },
    staff: { [unitId]: newStaffForUnit },
  };
};

// 將前端狀態轉換為後端 JSON 資料
export const transformStateToJson = (
  schedule: Schedule,
  staffData: StaffData,
  employees?: Employee[]
): JsonData => {
  if (!schedule || !schedule.roles || !schedule.timeslots) {
    throw new Error('Invalid schedule data');
  }

  const time_slots = schedule.timeslots.map((ts) => ts.split('-'));

  // 建立員工ID到員工資訊的映射
  const employeeMap = new Map<string, Employee>();

  // 優先使用最新的 employees 資料
  if (employees && employees.length > 0) {
    employees.forEach((employee) => {
      employeeMap.set(employee.employeeId, employee);
    });
  }

  // 回退到舊的 staffData
  const allStaffMap = new Map(
    Object.values(staffData)
      .flat()
      .map((s) => [s.id, s])
  );

  const group_infos: GroupInfo[] = schedule.roles.map((role) => {
    const workers: Worker[] = (role.roster || [])
      .map((staffId) => {
        // 優先使用最新的員工資料
        const employee = employeeMap.get(staffId);
        if (employee) {
          return {
            employee_id: employee.employeeId,
            employee_name: employee.employeeName,
          };
        }

        // 回退到舊的 staffData
        const staffMember = allStaffMap.get(staffId);
        return staffMember
          ? { employee_id: staffMember.id, employee_name: staffMember.name }
          : null;
      })
      .filter((w): w is Worker => w !== null);

    const requirement = schedule.timeslots.map(
      (slot) => schedule.headcount?.[role.id]?.[slot] || 0
    );

    return {
      group_id: role.id,
      group_name: role.name,
      requirement: requirement,
      workers: workers,
      note: role.remarks || '',
    };
  });

  return {
    time_slots,
    group_infos,
  };
};

// 生成員工分配摘要
export const generateStaffAssignments = (schedule: Schedule): { [staffId: string]: string[] } => {
  const assignments: { [staffId: string]: string[] } = {};

  if (!schedule?.roles) return assignments;

  schedule.roles.forEach((role) => {
    (role.roster || []).forEach((staffId) => {
      if (!assignments[staffId]) assignments[staffId] = [];
      assignments[staffId].push(role.name);
    });
  });

  return assignments;
};

// 增強 Schedule 資料，加入顯示用的文字
export const enhanceScheduleWithDisplayText = (
  schedule: Schedule,
  staffData: StaffData,
  employees?: Employee[]
): Schedule => {
  if (!schedule?.roles) return schedule;

  // 建立員工ID到員工名字的映射
  const employeeMap = new Map<string, string>();

  // 優先使用最新的 employees 資料
  if (employees && employees.length > 0) {
    employees.forEach((employee) => {
      employeeMap.set(employee.employeeId, employee.employeeName);
    });
  }

  // 回退到 staffData 中的資料
  const allStaffMap = new Map<string, StaffMember>(
    Object.values(staffData)
      .flat()
      .map((s) => [s.id, s])
  );

  return {
    ...schedule,
    roles: schedule.roles.map((role) => ({
      ...role,
      rosterText:
        (role.roster || [])
          .map((id) => {
            // 優先使用最新的員工資料
            const employeeName = employeeMap.get(id);
            if (employeeName) return employeeName;

            // 回退到舊的 staffData
            const staffMember = allStaffMap.get(id);
            return staffMember?.name || '未知';
          })
          .join(', ') || '未指派',
    })),
  };
};

// 驗證 Schedule 資料完整性
export const validateScheduleData = (
  schedule: Schedule
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!schedule) {
    errors.push('Schedule 資料為空');
    return { isValid: false, errors };
  }

  if (!schedule.roles || schedule.roles.length === 0) {
    errors.push('沒有角色資料');
  }

  if (!schedule.timeslots || schedule.timeslots.length === 0) {
    errors.push('沒有時間段資料');
  }

  if (!schedule.headcount) {
    errors.push('沒有人力需求資料');
  }

  // 檢查角色資料完整性
  schedule.roles?.forEach((role, index) => {
    if (!role.id) {
      errors.push(`角色 ${index + 1} 缺少 ID`);
    }
    if (!role.name) {
      errors.push(`角色 ${index + 1} 缺少名稱`);
    }
  });

  // 檢查時間段格式
  schedule.timeslots?.forEach((slot, index) => {
    if (!slot.includes('-')) {
      errors.push(`時間段 ${index + 1} 格式錯誤：${slot}`);
    }
  });

  return { isValid: errors.length === 0, errors };
};

// 合併兩個 Schedule 資料
export const mergeScheduleData = (base: Schedule, updates: Partial<Schedule>): Schedule => {
  return {
    roles: updates.roles || base.roles,
    timeslots: updates.timeslots || base.timeslots,
    headcount: updates.headcount || base.headcount,
  };
};

// 建立空白的 Schedule 資料
export const createEmptySchedule = (): Schedule => {
  return {
    roles: [],
    timeslots: [],
    headcount: {},
  };
};

// 建立預設的角色資料
export const createDefaultRole = (name: string): Role => {
  return {
    id: uuidv4(),
    name: name,
    roster: [],
    remarks: '',
  };
};

// 計算角色統計資訊
export const calculateRoleStats = (
  role: Role,
  schedule: Schedule
): {
  totalAssigned: number;
  totalRequirement: number;
  timeslotStats: { [slot: string]: { assigned: number; required: number } };
} => {
  const totalAssigned = role.roster?.length || 0;
  const timeslotStats: { [slot: string]: { assigned: number; required: number } } = {};
  let totalRequirement = 0;

  schedule.timeslots.forEach((slot) => {
    const required = schedule.headcount?.[role.id]?.[slot] || 0;
    totalRequirement += required;
    timeslotStats[slot] = {
      assigned: totalAssigned, // 假設所有指派的人員都在每個時段工作
      required: required,
    };
  });

  return {
    totalAssigned,
    totalRequirement,
    timeslotStats,
  };
};
