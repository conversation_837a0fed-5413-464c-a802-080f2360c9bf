# Shift Setting 目錄結構說明

## 概述

本文件詳細說明了 `shift_setting` 模組重構後的目錄結構、各文件的職責和相互關係。

## 目錄結構

```
src/components/ui/shift_setting/
├── shift.tsx                    # 主組件 (154行)
├── types.ts                     # 型別定義 (165行)
├── constants.tsx                # 常數定義 (116行)
├── components/                  # UI 組件目錄
│   ├── index.ts                # 組件統一導出
│   ├── LoadingOverlay.tsx      # 載入遮罩組件
│   ├── ScheduleTable.tsx       # 排班表格組件
│   ├── RosterModal.tsx         # 人員指派彈窗
│   └── ActionModal.tsx         # 動作確認彈窗
├── hooks/                       # 自定義 Hook 目錄
│   └── useShift.ts             # 主要業務邏輯 Hook (388行)
├── utils/                       # 工具函數目錄
│   ├── helpers.ts              # 通用工具函數
│   └── data-transform.ts       # 資料轉換函數
└── services/                    # API 服務目錄
    └── api.ts                  # API 客戶端和服務
```

## 文件職責說明

### 核心文件

#### 1. `shift.tsx` - 主組件
- **職責**：應用的主要入口點，負責UI渲染和組件組合
- **特點**：
  - 只包含渲染邏輯，沒有複雜的業務邏輯
  - 使用 `useShift` Hook 獲取所有狀態和函數
  - 結構清晰，易於理解和維護
- **主要內容**：
  - 狀態提示區域
  - 頁面標題和控制按鈕
  - 排班表格渲染
  - Modal 組件渲染

#### 2. `types.ts` - 型別定義
- **職責**：統一管理所有 TypeScript 型別定義
- **包含內容**：
  - API 回應的原始資料結構 (`Worker`, `GroupInfo`, `JsonData`)
  - 應用內部狀態結構 (`StaffMember`, `Role`, `Schedule`)
  - Modal 相關型別 (`ActionModalConfig`, `ActionType`)
  - 組件 Props 型別 (`LoadingOverlayProps`, `ScheduleTableProps`)
  - API 相關型別 (`ApiResponse`, `FetchConfig`)

#### 3. `constants.tsx` - 常數定義
- **職責**：集中管理配置、映射關係和靜態資料
- **包含內容**：
  - API 配置 (`API_CONFIG`)
  - 單位 ID 映射 (`UNIT_ID_MAPPING`)
  - 使用者提供的預設資料 (`USER_PROVIDED_JSON`)
  - 模擬資料 (`MOCK_DATA`)
  - 圖標定義 (`ICONS`)

### 組件目錄 (`components/`)

#### 1. `index.ts` - 統一導出
- **職責**：提供組件的統一導出入口
- **內容**：導出所有組件供外部使用

#### 2. `LoadingOverlay.tsx` - 載入遮罩
- **職責**：顯示載入狀態的遮罩組件
- **特點**：
  - 簡單的功能性組件
  - 使用 `ICONS.bigSpinner` 提供載入動畫
  - 可自定義載入文字

#### 3. `ScheduleTable.tsx` - 排班表格
- **職責**：顯示和編輯排班表格
- **特點**：
  - 使用 `React.memo` 優化性能
  - 支援時間段排序
  - 即時編輯功能
  - 複雜的表格佈局和互動

#### 4. `RosterModal.tsx` - 人員指派彈窗
- **職責**：處理人員指派的彈窗介面
- **特點**：
  - 支援搜尋和篩選功能
  - 顯示人員衝突資訊
  - 可批量選擇人員
  - 即時預覽指派結果

#### 5. `ActionModal.tsx` - 動作確認彈窗
- **職責**：處理各種動作確認的彈窗
- **特點**：
  - 支援多種模式 (`confirm`, `time-range`)
  - 動態標題和內容
  - 時間範圍選擇功能
  - 統一的確認/取消介面

### Hook 目錄 (`hooks/`)

#### 1. `useShift.ts` - 主要業務邏輯 Hook
- **職責**：封裝所有複雜的狀態管理和業務邏輯
- **功能模組**：
  - **狀態管理**：管理所有應用狀態
  - **資料載入**：處理 API 資料載入
  - **資料儲存**：處理設定的儲存
  - **排班更新**：處理排班資料的更新
  - **Modal 管理**：管理各種彈窗狀態
  - **錯誤處理**：統一的錯誤處理邏輯
- **回傳值**：
  - 狀態：`currentUnitId`, `isLoading`, `saveStatus` 等
  - 計算屬性：`scheduleWithRosterText`, `staffAssignments` 等
  - 動作函數：`loadData`, `handleSaveSettings`, `handleScheduleUpdate` 等

### 工具函數目錄 (`utils/`)

#### 1. `helpers.ts` - 通用工具函數
- **職責**：提供可重用的純函數
- **主要函數**：
  - `getTimeValue`: 時間字串轉換為數值
  - `formatTime`: 格式化時間顯示
  - `sortTimeslots`: 時間段排序
  - `isTimeOverlapping`: 檢查時間重疊
  - `deepClone`: 深度複製對象
  - `debounce`: 防抖函數
  - `generateId`: 生成唯一 ID
  - `getErrorMessage`: 錯誤信息提取

#### 2. `data-transform.ts` - 資料轉換
- **職責**：處理複雜的資料轉換邏輯
- **主要函數**：
  - `transformJsonToState`: API 資料轉換為應用狀態
  - `transformStateToJson`: 應用狀態轉換為 API 格式
  - `enhanceScheduleWithDisplayText`: 增強排班資料顯示
  - `generateStaffAssignments`: 生成人員指派關係
  - `createDefaultRole`: 創建預設角色
  - `validateScheduleData`: 驗證排班資料

### 服務目錄 (`services/`)

#### 1. `api.ts` - API 客戶端和服務
- **職責**：統一管理所有 API 調用
- **設計特點**：
  - 可抽換的 API 客戶端設計
  - 支援未來遷移到 axios + react-query
  - 統一的錯誤處理
  - 型別安全的 API 介面
- **主要類別**：
  - `ApiClient`: 基礎 API 客戶端
  - `ShiftApiService`: 排班相關 API 服務
- **功能**：
  - `getRequirements`: 獲取需求資料
  - `saveRequirements`: 儲存需求資料
  - `validateUnitId`: 驗證單位 ID

## 架構優勢

### 1. 關注點分離
- **UI 組件**：只負責顯示和基本互動
- **業務邏輯**：封裝在 Hook 中
- **資料處理**：獨立的工具函數
- **API 調用**：統一的服務層

### 2. 可維護性
- **模組化設計**：每個檔案職責單一
- **型別安全**：完整的 TypeScript 支援
- **統一介面**：一致的 API 設計
- **清晰命名**：直觀的檔案和函數命名

### 3. 可擴展性
- **組件復用**：組件可在其他地方使用
- **Hook 復用**：Hook 可以被其他組件使用
- **工具函數**：純函數易於測試和復用
- **API 抽象**：易於切換不同的 API 實現

### 4. 性能優化
- **React.memo**：避免不必要的重渲染
- **useMemo**：緩存計算結果
- **useCallback**：優化函數引用
- **代碼分割**：模組化載入

## 依賴關係

### 依賴層級
```
shift.tsx (主組件)
├── hooks/useShift.ts
│   ├── services/api.ts
│   ├── utils/data-transform.ts
│   │   └── utils/helpers.ts
│   ├── constants.tsx
│   └── types.ts
├── components/
│   ├── LoadingOverlay.tsx
│   │   ├── constants.tsx (ICONS)
│   │   └── types.ts
│   ├── ScheduleTable.tsx
│   │   ├── utils/helpers.ts
│   │   └── types.ts
│   ├── RosterModal.tsx
│   │   ├── utils/helpers.ts
│   │   └── types.ts
│   └── ActionModal.tsx
│       ├── utils/helpers.ts
│       └── types.ts
├── constants.tsx
└── types.ts
```

### 依賴原則
- **單向依賴**：避免循環依賴
- **最小依賴**：只引入必需的模組
- **清晰界限**：每層職責明確
- **易於測試**：依賴可以模擬

## 重構前後對比

### 重構前 (`shift.tsx`)
- **文件大小**：932 行
- **組件定義**：多個重複的組件定義
- **業務邏輯**：混合在主組件中
- **狀態管理**：複雜的 useState 和 useEffect
- **API 調用**：散佈在不同地方
- **型別定義**：內聯定義，缺乏復用

### 重構後 (模組化架構)
- **主文件大小**：154 行 (減少 83%)
- **組件分離**：每個組件獨立檔案
- **業務邏輯**：封裝在 useShift Hook
- **狀態管理**：集中管理，清晰的資料流
- **API 調用**：統一的服務層
- **型別定義**：統一管理，完整復用

## 最佳實踐

### 1. 檔案命名
- **組件**：PascalCase (`LoadingOverlay.tsx`)
- **Hook**：camelCase with use prefix (`useShift.ts`)
- **工具函數**：kebab-case (`data-transform.ts`)
- **型別檔案**：lowercase (`types.ts`)

### 2. 導出規範
- **預設導出**：組件使用預設導出
- **命名導出**：工具函數和型別使用命名導出
- **統一導出**：目錄使用 `index.ts` 統一導出

### 3. 註釋規範
- **檔案頭註釋**：說明檔案職責
- **複雜邏輯註釋**：解釋複雜的業務邏輯
- **TODO 註釋**：標記待改進的地方
- **型別註釋**：複雜型別的說明

### 4. 錯誤處理
- **統一錯誤型別**：使用一致的錯誤處理
- **錯誤邊界**：適當的錯誤邊界設定
- **用戶友好**：提供清晰的錯誤信息
- **日誌記錄**：適當的錯誤日誌

## 未來擴展建議

### 1. 測試框架
- **單元測試**：每個工具函數和 Hook
- **組件測試**：React Testing Library
- **整合測試**：端到端測試
- **性能測試**：性能基準測試

### 2. 狀態管理升級
- **Redux Toolkit**：複雜狀態管理
- **Zustand**：輕量級狀態管理
- **React Query**：伺服器狀態管理
- **Context API**：跨組件狀態共享

### 3. 性能優化
- **代碼分割**：React.lazy 和 Suspense
- **虛擬滾動**：大量資料的表格
- **記憶化**：更細粒度的 memo
- **Web Workers**：重計算任務

### 4. 開發工具
- **Storybook**：組件開發和文檔
- **React DevTools**：除錯工具
- **Bundle Analyzer**：打包分析
- **ESLint/Prettier**：代碼品質工具

## 結論

這個重構後的目錄結構提供了：
- **清晰的職責分離**
- **良好的可維護性**
- **優秀的可擴展性**
- **強的型別安全**
- **高效的開發體驗**

通過這種模組化的架構，團隊可以更容易地：
- 理解代碼結構
- 添加新功能
- 修復錯誤
- 進行重構
- 編寫測試

這個架構可以作為其他類似專案的參考範本，幫助建立更好的代碼組織和開發實踐。 