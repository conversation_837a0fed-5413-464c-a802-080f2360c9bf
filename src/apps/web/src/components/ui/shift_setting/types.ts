// --- 型別定義 (Type Definitions) ---

// API 回應的原始資料結構
export interface Worker {
  employee_id: string;
  employee_name: string;
}

export interface GroupInfo {
  group_id: string;
  group_name: string;
  requirement: number[];
  workers: Worker[];
  note: string;
}

export interface JsonData {
  time_slots: string[][];
  group_infos: GroupInfo[];
}

// 應用程式內部狀態的資料結構
export interface StaffMember {
  id: string;
  name: string;
  employeeIds?: string;
}

export interface Role {
  id: string;
  name: string;
  roster: string[]; // 儲存 StaffMember 的 id
  remarks: string;
  rosterText?: string; // 用於顯示的文字，由 useMemo 產生
}

export interface Headcount {
  [roleId: string]: {
    [timeslot: string]: number;
  };
}

export interface Schedule {
  roles: Role[];
  timeslots: string[];
  headcount: Headcount;
}

export interface StaffData {
  [unitId: string]: StaffMember[];
}

export interface StaffAssignments {
  [staffId: string]: string[];
}

export interface Unit {
  id: string;
  name: string;
}

// Modal 相關型別
export type ActionType = 'delete-role' | 'add-timeslot' | 'delete-timeslot';

export interface BaseActionModalConfig {
  type: ActionType;
  title: string;
  message?: string;
  mode: 'confirm' | 'time-range';
  onConfirm: (type: ActionType, data: any) => void;
  onClose: () => void;
}

export interface DeleteRoleAction extends BaseActionModalConfig {
  type: 'delete-role';
  data: Role;
}

export interface DeleteTimeslotAction extends BaseActionModalConfig {
  type: 'delete-timeslot';
  data: string;
}

export interface AddTimeslotAction extends BaseActionModalConfig {
  type: 'add-timeslot';
  data?: { start: string; end: string };
}

export type ActionModalConfig =
  | DeleteRoleAction
  | DeleteTimeslotAction
  | AddTimeslotAction
  | {
      title: string;
      message: string;
      onConfirm: () => void;
      onClose: () => void;
    };

// 組件 Props 型別
export interface LoadingOverlayProps {
  text: string;
}

export interface ScheduleTableProps {
  schedule: Schedule;
  onUpdate: (
    roleId: string,
    field: 'roleName' | 'remarks' | 'headcount',
    value: string | number,
    timeslot?: string | null
  ) => void;
  onShowRosterModal: (role: Role) => void;
  onShowActionModal: (
    config: Omit<ActionModalConfig, 'onConfirm' | 'onClose' | 'title' | 'message' | 'mode'> & {
      type: ActionType;
      data?: any;
    }
  ) => void;
}

export interface RosterModalProps {
  isOpen: boolean;
  onClose: () => void;
  role: Role | null;
  departmentId: string;
  onSave: (roleId: string, roster: string[]) => void;
  staffAssignments: StaffAssignments;
}

export interface ActionModalProps {
  config: ActionModalConfig | null;
  onConfirm: (type: ActionType, data: any) => void;
  onClose: () => void;
}

export interface StaffItemProps {
  staff: Employee;
  isAssigned: boolean;
  assignments: StaffAssignments;
}

// 應用程式狀態相關
export interface SaveStatus {
  msg: string;
  visible: boolean;
  isError: boolean;
}

// API 相關型別
export interface AxiosConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  data?: any;
  signal?: AbortSignal; // React Query 支援
  timeout?: number;
  withCredentials?: boolean;
}

// 員工相關型別
export interface Employee {
  companyId: string;
  employeeId: string;
  employeeNumber: string;
  employeeName: string;
  departmentId: string;
}

export interface GetEmployeesResponse {
  data: Employee[];
  message: string;
}

export interface GetEmployeesQueryParams {
  ids?: string[];
  deptIds?: string[];
}

// 統一錯誤類型
export interface ApiError {
  message: string;
  code?: string | number;
  status?: number;
  details?: any;
}

// 增強的 API 響應類型
export interface ApiResponse<T> {
  data: T;
  error?: ApiError;
  timestamp?: string;
}

// React Query 相關類型（未來使用）
export interface QueryOptions {
  enabled?: boolean;
  staleTime?: number;
  cacheTime?: number;
  refetchOnWindowFocus?: boolean;
}

// 查詢鍵類型
export type ShiftQueryKey =
  | ['shift', 'requirements', string]
  | ['shift', 'validation', string]
  | ['shift'];

// 移除舊的 FetchConfig（保留註釋說明遷移）
/**
 * @deprecated 使用 AxiosConfig 替代
 * 此類型已遷移至 AxiosConfig 以支援 axios
 */
export interface FetchConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  credentials?: RequestCredentials;
}
