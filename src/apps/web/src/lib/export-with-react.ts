import React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import dayjs from 'dayjs';
import ShiftSchedulerExport from '@/components/ui/shift/shift-scheduler-export';
import {
  ShiftDataResponse,
  Employee as ApiEmployee,
  CalendarDay,
  ShiftDefinition,
} from '@/datas/api.types';
import {
  Employee,
  ShiftItem,
  ShiftType,
  extractEmployeesFromApi,
} from '@/components/ui/shift/shift-constants';

// 更新後的導出選項介面，支持 API 資料
interface ExportOptions {
  currentMonth?: dayjs.Dayjs;
  apiData?: ShiftDataResponse; // 新增：直接接收 API 資料
  shifts?: ShiftItem[];
  employees?: Employee[];
  shiftTypes?: ShiftType[];
  title?: string;
  startDate?: string; // 開始日期，格式：YYYY-MM-DD
  endDate?: string; // 結束日期，格式：YYYY-MM-DD
}

/**
 * 使用React組件生成並下載HTML檔案
 * @param options 導出選項
 */
export const exportScheduleWithReact = (options: ExportOptions = {}) => {
  try {
    // 使用 React 組件生成 HTML
    const htmlContent = renderToStaticMarkup(React.createElement(ShiftSchedulerExport, options));

    // 添加DOCTYPE
    const fullHtmlContent = `<!DOCTYPE html>\n${htmlContent}`;

    // 創建並下載檔案
    const blob = new Blob([fullHtmlContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);

    const month = options.currentMonth || dayjs();
    const title = options.title || `班表排程 - ${month.format('YYYY年MM月')}`;
    const fileName = `${title.replace(/[/:]/g, '-')}_${month.format('YYYY-MM')}.html`;

    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理 URL
    URL.revokeObjectURL(url);

    console.log(`✅ 成功下載: ${fileName}`);
  } catch (error) {
    console.error('❌ 導出HTML時發生錯誤:', error);
  }
};

/**
 * 生成HTML字串（不下載）
 * @param options 導出選項
 * @returns HTML字串
 */
export const generateScheduleWithReact = (options: ExportOptions = {}): string => {
  try {
    // 如果有 apiData，直接傳遞給組件
    const exportProps = options.apiData
      ? {
          ...options,
          apiData: options.apiData,
        }
      : options;

    const htmlContent = renderToStaticMarkup(
      React.createElement(ShiftSchedulerExport, exportProps)
    );

    return `<!DOCTYPE html>\n${htmlContent}`;
  } catch (error) {
    console.error('❌ 生成HTML時發生錯誤:', error);
    return '';
  }
};

/**
 * 從 API 資料結構生成測試用的 ShiftItem 陣列
 */
export const generateTestShiftsFromApiStructure = (year: number, month: number): ShiftItem[] => {
  const targetMonth = dayjs()
    .year(year)
    .month(month - 1);
  const daysInMonth = targetMonth.daysInMonth();
  const shifts: ShiftItem[] = [];

  // 基於 API 結構的預設班次 ID
  const shiftScheduleIds = [
    '7f51d53e-2d6f-449a-a12b-447a1a341af6', // 王永慶正常班
    '2cffd57e-5d96-49e1-b609-2dbf21d53421', // 陳湘頤中班
    'dc129ad6-32d2-4924-8df3-0fee0ef127c1', // 放假班
    '00000000-0000-0000-0000-000000000003', // 例假日
  ];

  // 使用 20 個員工以匹配 defaultEmployees 數組
  const employeeNumbers = Array.from(
    { length: 20 },
    (_, i) => `E000${String(i + 1).padStart(2, '0')}`
  );

  employeeNumbers.forEach((employeeNumber) => {
    for (let day = 1; day <= daysInMonth; day++) {
      const date = targetMonth.date(day).format('YYYY-MM-DD');
      const dayObj = targetMonth.date(day);
      const isWeekend = dayObj.day() === 0 || dayObj.day() === 6;

      // 使用 Math.random() * 0.5 來限制隨機範圍，主要生成單一班次
      const random = Math.random() * 0.5; // 限制到 0-0.5 範圍

      if (isWeekend && random < 0.3) {
        // 週末 30% 機率例假日
        // 週末例假日
        const calendarDay: ShiftItem = {
          date: `${date}T00:00:00+00:00`,
          shiftId: null,
          cycleSn: 0,
          billingStatus: 0,
          isBilling: false,
          shiftSchedule: null,
          supportDeptId: null,
          supportDeptName: null,
          calendarEvent: null,
          leaveSheets: null,
          overtimeSheets: null,
          partialSupport: null,
          tripSheets: null,
          selectShiftSchedule: false,
          arrangeLeave: false,
          isShiftRules: false,
          adjustmentScheduleTime: false,
          advanceLeave: false,
          isEditable: false,
          isAllLeaveRequestFormsApproved: null,
          itemOptionId: null,
          finalBilling: true,
          clockedInTime: null,
          dayStartTime: '0001-01-01T00:00:00+00:00',
          latestUpdateTime: '0001-01-01T00:00:00+00:00',
          latestUpdaterName: null,
          schedulingStatus: 3, // 例假日
          employeeId: employeeNumber,
          employeeName: `員工${employeeNumber}`,
        };
        shifts.push(calendarDay);
      } else if (random < 0.4) {
        // 40% 機率有排班（確保大多數是單一班次）
        // 有排班
        const shiftScheduleId = shiftScheduleIds[Math.floor(Math.random() * 2)]; // 只使用前兩個工作班次
        const cycleSn = Math.floor(Math.random() * 7) + 1;

        // 根據班次ID設定不同的時間
        const isEarlyShift = shiftScheduleId === '7f51d53e-2d6f-449a-a12b-447a1a341af6';
        const workOnTime = isEarlyShift ? `${date}T08:00:00+00:00` : `${date}T16:00:00+00:00`;
        const workOffTime = isEarlyShift
          ? `${date}T16:00:00+00:00`
          : `${dayObj.add(1, 'day').format('YYYY-MM-DD')}T00:00:00+00:00`;

        // 不添加休息時間，確保生成單一班次
        const calendarDay: ShiftItem = {
          date: `${date}T00:00:00+00:00`,
          shiftId: '80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004',
          cycleSn: cycleSn,
          billingStatus: 0,
          isBilling: false,
          shiftSchedule: {
            restTimeStart1: null, // 不設定休息時間
            restTimeEnd1: null,
            restTimeStart2: null,
            restTimeEnd2: null,
            restTimeStart3: null,
            restTimeEnd3: null,
            shiftScheduleId: shiftScheduleId,
            cycleSn: cycleSn,
            cycleStatus: 1,
            colorCode: isEarlyShift ? '#FE9C4E' : '#07C1B6',
            shiftScheduleName: isEarlyShift ? '王永慶正常班' : '陳湘頤中班',
            isWorkTimeChanged: false,
            workOnTime: workOnTime,
            workOffTime: workOffTime,
            originalWorkOnTime: null,
            originalWorkOffTime: null,
            restMinutes: null,
          },
          supportDeptId: null,
          supportDeptName: null,
          calendarEvent: null,
          leaveSheets: [],
          overtimeSheets: [],
          partialSupport: [],
          tripSheets: [],
          selectShiftSchedule: true,
          arrangeLeave: true,
          isShiftRules: true,
          adjustmentScheduleTime: true,
          advanceLeave: true,
          isEditable: true,
          isAllLeaveRequestFormsApproved: null,
          itemOptionId: 'CY00001',
          finalBilling: false,
          clockedInTime: null,
          dayStartTime: `${dayObj.subtract(1, 'day').format('YYYY-MM-DD')}T16:00:00+00:00`,
          latestUpdateTime: '2024-11-01T02:56:49+00:00',
          latestUpdaterName: '',
          schedulingStatus: 1, // 有排班
          employeeId: employeeNumber,
          employeeName: `員工${employeeNumber}`,
        };
        shifts.push(calendarDay);
      }
      // 剩下 60% 不排班（增加未排班比例）
    }
  });

  return shifts;
};

/**
 * 舊版生成測試數據 - 保持向下相容
 */
export const generateTestShifts = (year: number, month: number): ShiftItem[] => {
  return generateTestShiftsFromApiStructure(year, month);
};

/**
 * 從 API 資料轉換為導出格式
 */
export const convertApiDataToExportFormat = (
  apiData: ShiftDataResponse
): {
  employees: Employee[];
  shifts: ShiftItem[];
  shiftTypes: ShiftType[];
} => {
  const employees = extractEmployeesFromApi(apiData);

  // 處理新的 shiftsDefinition 結構
  const allShiftDefinitions = [
    ...(apiData.shiftsDefinition.monthLeaves || []),
    ...(apiData.shiftsDefinition.shifts || []),
  ];

  const shiftTypes = allShiftDefinitions.map((shift: ShiftDefinition) => ({
    ...shift,
    title: shift.shiftScheduleName,
    id: shift.shiftScheduleId,
    color: shift.colorCode,
  }));

  // 將所有員工的 calendars 展平為 ShiftItem 陣列
  const shifts = apiData.employees.flatMap((employee: ApiEmployee) =>
    employee.calendars.map((calendar: CalendarDay) => ({
      ...calendar,
      employeeId: employee.employeeNumber,
      employeeName: employee.chineseName,
    }))
  );

  return {
    employees,
    shifts,
    shiftTypes,
  };
};

/**
 * 快捷導出函數
 */
export const quickExportReact = {
  // 下載當前月份
  currentMonth: () => {
    exportScheduleWithReact();
  },

  // 下載指定月份
  specificMonth: (year: number, month: number) => {
    const targetMonth = dayjs()
      .year(year)
      .month(month - 1);
    exportScheduleWithReact({ currentMonth: targetMonth });
  },

  // 下載上個月
  lastMonth: () => {
    const lastMonth = dayjs().subtract(1, 'month');
    exportScheduleWithReact({
      currentMonth: lastMonth,
      title: `上個月班表 - ${lastMonth.format('YYYY年MM月')}`,
    });
  },

  // 下載下個月
  nextMonth: () => {
    const nextMonth = dayjs().add(1, 'month');
    exportScheduleWithReact({
      currentMonth: nextMonth,
      title: `下個月班表 - ${nextMonth.format('YYYY年MM月')}`,
    });
  },

  // 下載含測試數據的當前月份
  currentMonthWithTestData: () => {
    const now = dayjs();
    const testShifts = generateTestShiftsFromApiStructure(now.year(), now.month() + 1);

    // 生成測試用的 API 資料結構
    const testApiData: ShiftDataResponse = {
      type: 'shift-data',
      startDate: now.startOf('month').format('YYYY-MM-DD'),
      endDate: now.endOf('month').format('YYYY-MM-DD'),
      companyId: '123',
      departmentId: '123',
      departments: [],
      employees: Array.from({ length: 5 }, (_, i) => ({
        chineseName: `測試員工${i + 1}`,
        employeeNumber: `E000${i + 1}`,
        schedulingStatus: 1,
        totalMonthLeaveTime: 0,
        totalScheduleLeaveTime: 0,
        fatigueValue: 0,
        schedulingStatusRangeMap: {},
        empSchedulingStartDate: '0001-01-01T00:00:00+00:00',
        empSchedulingEndDate: '0001-01-01T00:00:00+00:00',
        supervisorSchedulingStartDate: '0001-01-01T00:00:00+00:00',
        supervisorSchedulingEndDate: '0001-01-01T00:00:00+00:00',
        selectShiftSchedule: true,
        arrangeLeave: true,
        isShiftRules: true,
        adjustmentScheduleTime: true,
        advanceLeave: true,
        isEditable: true,
        isSupervisorEditable: true,
        calendars: testShifts.filter((shift) => shift.employeeId === `E000${i + 1}`),
        totalWorkTime: 0,
        totalLeaveTime: 0,
        totalOverTime: 0,
        totalTripMinutes: 0,
        monthLeaveDays: 0,
        monthLeaveDaysUsed: 0,
      })),
      shiftsDefinition: {
        monthLeaves: [],
        shifts: [
          {
            shiftIds: ['80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004'],
            cycles: [],
            dayStartTime: '2010-10-09T16:00:00+00:00',
            shiftScheduleId: '7f51d53e-2d6f-449a-a12b-447a1a341af6',
            shiftScheduleName: '王永慶正常班',
            colorCode: '#FE9C4E',
            remnant: null,
          },
          {
            shiftIds: ['6dc300d2-6ead-4d79-96e4-29a40b320139'],
            cycles: [],
            dayStartTime: '2010-10-09T16:00:00+00:00',
            shiftScheduleId: '2cffd57e-5d96-49e1-b609-2dbf21d53421',
            shiftScheduleName: '陳湘頤中班',
            colorCode: '#07C1B6',
            remnant: null,
          },
        ],
      },
      departmentName: '內場',
      companyName: '金豬中山店',
      ShiftScheduleDashBoard: [],
    };

    exportScheduleWithReact({
      currentMonth: now,
      apiData: testApiData,
      title: `金豬中山店內場 班表 ${now.format('YYYY/MM/DD')} ~ ${now.add(1, 'month').format('YYYY/MM/DD')}`,
    });
  },

  // 下載自定義數據
  custom: (options: ExportOptions) => {
    exportScheduleWithReact(options);
  },
};

/**
 * 批量下載多個月份
 */
export const batchExportReact = (
  startYear: number,
  startMonth: number,
  monthCount: number,
  apiData?: ShiftDataResponse
) => {
  for (let i = 0; i < monthCount; i++) {
    const targetMonth = dayjs()
      .year(startYear)
      .month(startMonth - 1)
      .add(i, 'month');

    // 延遲下載避免瀏覽器阻止多重下載
    setTimeout(() => {
      exportScheduleWithReact({
        currentMonth: targetMonth,
        apiData: apiData,
        title: `班表排程 - ${targetMonth.format('YYYY年MM月')}`,
      });
    }, i * 1000);
  }
};

// 導出類型供其他文件使用
export type { ExportOptions };
// 重新導出來自 shift-constants 的類型
export type { ShiftType, Employee, ShiftItem };
