/* eslint-disable no-console */
import * as postRobot from 'post-robot';

type PostRobotHandler = {
  cancel: () => void;
};

export const sendMessage = async (
  targetWindow: Window,
  messageType: string,
  messageData: any
): Promise<void> => {
  try {
    const response = await postRobot.send(targetWindow, messageType, messageData);
    console.log('Message sent successfully:', response.data);
  } catch (error) {
    console.error('Error sending message:', error);
  }
};

export const setupListener = (
  messageType: string,
  callback: (data: any) => void
): PostRobotHandler => {
  const listener = postRobot.on(messageType, (event) => {
    callback(event.data);
    return Promise.resolve({ status: 'Received successfully' });
  });

  // Return the listener for potential cancellation
  return listener;
};