/**
 * 應用程式共用常量定義
 *
 * 此文件包含整個應用程式中需要共用的常量、枚舉和類型定義
 */

// 命令類型枚舉 - 用於 AI 聊天系統的對話額外執行的命令分類
export enum CommandType {
  // 刷新
  REFRESH = 'refresh',
  // 觸發AI歡迎語
  GREETING_SHIFT_SCHEDULE = 'greeting-shift-schedule',
}

// 命令相關的類型定義
export interface CommandPayload {
  type: CommandType;
  payload?: any;
}

// 聊天消息相關常量
export const CHAT_CONSTANTS = {
  // 消息 ID 前綴
  DO_NOT_RENDER_ID_PREFIX: 'DO_NOT_RENDER_',

  // 消息類型
  MESSAGE_TYPES: {
    HUMAN: 'human',
    AI: 'ai',
    TOOL: 'tool',
  } as const,
} as const;

// 導出常用類型
export type MessageType =
  (typeof CHAT_CONSTANTS.MESSAGE_TYPES)[keyof typeof CHAT_CONSTANTS.MESSAGE_TYPES];

// PostMessage 訊息類型定義
export const POST_MESSAGE_TYPES = {
  // 資料相關
  REQUEST_PROCESSED_DATA: 'request-processed-data',
  PROCESSED_DATA_RESPONSE: 'processed-data-response',
  REQUEST_SCHEDULE_DATA: 'request-schedule-data',
  SCHEDULE_DATA_RESPONSE: 'schedule-data-response',

  // 操作相關
  SCHEDULE_REFRESH: 'schedule-refresh',
  SCHEDULE_SETTINGS_FULL_SCREEN: 'schedule-settings-full-screen',
  SCHEDULE_SETTINGS_CLOSE_FULL_SCREEN: 'schedule-settings-close-full-screen',

  // 系統相關
  HEALTH_CHECK: 'health-check',
  HEALTH_CHECK_RESPONSE: 'health-check-response',
} as const;

// PostMessage 類型
export type PostMessageType = (typeof POST_MESSAGE_TYPES)[keyof typeof POST_MESSAGE_TYPES];

// PostMessage 資料結構
export interface PostMessageData<T = any> {
  type: PostMessageType;
  payload: T;
  timestamp: string;
  source?: string;
}

// 特定業務邏輯的 PostMessage 資料類型
export interface ProcessedDataRequest {
  messages: any[];
  timestamp: string;
}

export interface ScheduleDataRequest {
  timestamp: string;
  source?: Window;
}

export interface ScheduleDataResponse {
  messages: any[];
  timestamp: string;
}

// PostMessage 事件處理器類型
export type PostMessageHandler<T = any> = (data: T, event?: MessageEvent) => void | Promise<void>;

// PostMessage 錯誤類型
export interface PostMessageError {
  type: PostMessageType;
  message: string;
  timestamp: string;
  originalError?: Error;
}
