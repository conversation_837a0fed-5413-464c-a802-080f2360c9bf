/**
 * 班次排程管理系統的 TypeScript 類型定義
 * 對應 api.json 的資料結構
 */

// 主要根層級介面
export interface ShiftDataResponse {
  type: 'shift-data';
  employees: Employee[];
  shiftsDefinition: {
    monthLeaves: ShiftDefinition[];
    shifts: ShiftDefinition[];
  };
  startDate: string;
  endDate: string;
  departmentId: string;
  departmentName: string;
  companyName: string;
  companyId: string;
  ShiftScheduleDashBoard: ShiftScheduleDashBoard[];
  departments: Department[];
}

export interface Department {
  departmentId: string;
  deptCName: string;
  deptCode: string;
  deptEName: string;
}

// 員工資料介面
export interface Employee {
  chineseName: string;
  employeeNumber: string;
  schedulingStatus: number;
  totalMonthLeaveTime: number;
  totalScheduleLeaveTime: number;
  fatigueValue: number;
  schedulingStatusRangeMap: SchedulingStatusRangeMap;
  empSchedulingStartDate: string;
  empSchedulingEndDate: string;
  supervisorSchedulingStartDate: string;
  supervisorSchedulingEndDate: string;
  selectShiftSchedule: boolean;
  arrangeLeave: boolean;
  isShiftRules: boolean;
  adjustmentScheduleTime: boolean;
  advanceLeave: boolean;
  isEditable: boolean;
  isSupervisorEditable: boolean;
  calendars: CalendarDay[];
  totalWorkTime: number;
  totalLeaveTime: number;
  totalOverTime: number;
  totalTripMinutes: number;
  monthLeaveDays: number;
  monthLeaveDaysUsed: number;
}

// 排班狀態範圍對應表
export interface SchedulingStatusRangeMap {
  [statusCode: string]: DateRange[];
}

// 日期範圍
export interface DateRange {
  startDate: string;
  endDate: string;
}

// 日曆天資料
export interface CalendarDay {
  date: string;
  shiftId: string | null;
  cycleSn: number;
  billingStatus: number;
  isBilling: boolean;
  shiftSchedule: ShiftSchedule | null;
  supportDeptId: string | null;
  supportDeptName: string | null;
  calendarEvent: CalendarEvent | null;
  leaveSheets: LeaveSheet[] | null;
  overtimeSheets: OvertimeSheet[] | null;
  partialSupport: PartialSupport[] | null;
  tripSheets: TripSheet[] | null;
  selectShiftSchedule: boolean;
  arrangeLeave: boolean;
  isShiftRules: boolean;
  adjustmentScheduleTime: boolean;
  advanceLeave: boolean;
  isEditable: boolean;
  isAllLeaveRequestFormsApproved: boolean | null;
  itemOptionId: string | null;
  finalBilling: boolean;
  clockedInTime: string | null;
  dayStartTime: string;
  latestUpdateTime: string;
  latestUpdaterName: string | null;
  schedulingStatus: number;
}

// 班次排程詳細資訊
export interface ShiftSchedule {
  restTimeStart1: string | null;
  restTimeEnd1: string | null;
  restTimeStart2: string | null;
  restTimeEnd2: string | null;
  restTimeStart3: string | null;
  restTimeEnd3: string | null;
  shiftScheduleId: string;
  cycleSn: number;
  cycleStatus: number;
  colorCode: string;
  shiftScheduleName: string;
  isWorkTimeChanged: boolean;
  workOnTime: string | null;
  workOffTime: string | null;
  originalWorkOnTime: string | null;
  originalWorkOffTime: string | null;
  restMinutes: number | null;
}

// 行事曆事件
export interface CalendarEvent {
  eventStatus: number;
  itemOptionId: string | null;
  [key: string]: any;
}

// 請假單
export interface LeaveSheet {
  // 根據實際需求定義結構
  [key: string]: any;
}

// 加班單
export interface OvertimeSheet {
  // 根據實際需求定義結構
  [key: string]: any;
}

// 部分支援
export interface PartialSupport {
  // 根據實際需求定義結構
  [key: string]: any;
}

// 出差單
export interface TripSheet {
  // 根據實際需求定義結構
  [key: string]: any;
}

// 班次定義
export interface ShiftDefinition {
  shiftIds: string[];
  cycles: ShiftCycle[];
  dayStartTime: string;
  shiftScheduleId: string;
  shiftScheduleName: string;
  colorCode: string;
  remnant: any | null;
}

// 班次週期
export interface ShiftCycle {
  restTimeStart1: string | null;
  restTimeEnd1: string | null;
  restTimeStart2: string | null;
  restTimeEnd2: string | null;
  restTimeStart3: string | null;
  restTimeEnd3: string | null;
  itemOptionId: string;
  cycleSn: number;
  cycleStatus: number;
  workOnTime: string | null;
  workOffTime: string | null;
}

// 常用的枚舉類型
export enum SchedulingStatus {
  UNKNOWN = 0,
  ACTIVE = 1,
  INACTIVE = 2,
  HOLIDAY = 3,
}

export enum CycleStatus {
  WORK_DAY = 1,
  HOLIDAY = 2,
}

/**
 * 定義班別週期的類型
 */
export enum CycleType {
  /** 上班 (週期) */
  WorkDay = 'CY00001',
  /** 休假 (週期) */
  Vacation = 'CY00002',
  /** 休息日 (週期) */
  Rest = 'CY00003',
  /** 例假日 (週期) */
  Official = 'CY00004',
}

/**
 * 定義行事曆日的特殊類型
 */
export enum CalendarDayType {
  /** 特殊休假日 */
  Holiday = '00001',
  /** 特殊上班日 */
  WorkDay = '00002',
  /** 假日出勤給薪 */
  PayDay = '00003',
  /** 休息日 (行事曆) */
  RestDay = '00004',
  /** 例假日 (行事曆) */
  OfficialHoliday = '00005',
  /** 國定假日 */
  NationalHoliday = '00006',
}

export enum BillingStatus {
  NOT_BILLED = 0,
  BILLED = 1,
}

// 常用的工具類型
export type EmployeeId = string;
export type ShiftId = string;
export type ShiftScheduleId = string;
export type DateString = string; // ISO 8601 格式
export type ColorCode = string; // 十六進制顏色代碼

// 常用的選擇器類型
export interface EmployeeSelector {
  employeeNumber?: string;
  chineseName?: string;
}

export interface DateRangeSelector {
  startDate: DateString;
  endDate: DateString;
}

export interface ShiftSelector {
  shiftScheduleId?: string;
  shiftScheduleName?: string;
}

// 班次排程儀表板相關介面
export interface ShiftScheduleDashBoard {
  ShiftScheduleId: string;
  ColorCode: string;
  ShiftScheduleName: string;
  WorkTimes: number[];
  SupportTimes: number[];
  Totals: number[];
}

// 班次排程儀表板回應介面
export interface ShiftScheduleDashBoardResponse {
  ShiftScheduleDashBoard: ShiftScheduleDashBoard[];
}
