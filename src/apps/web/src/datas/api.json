{"type": "shift-data", "employees": [{"chineseName": "dd", "employeeNumber": "D0000123", "schedulingStatus": 0, "totalMonthLeaveTime": 0, "totalScheduleLeaveTime": 0, "fatigueValue": 0, "schedulingStatusRangeMap": {"3": [{"startDate": "2025-06-01T00:00:00+00:00", "endDate": "2025-06-30T00:00:00+00:00"}]}, "empSchedulingStartDate": "0001-01-01T00:00:00+00:00", "empSchedulingEndDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingStartDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingEndDate": "0001-01-01T00:00:00+00:00", "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isSupervisorEditable": false, "calendars": [{"date": "2025-06-01T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-02T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-03T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-04T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-05T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-06T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-07T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-08T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-09T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-10T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-11T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-12T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-13T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-14T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-15T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-16T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-17T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-18T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-19T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-20T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-21T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-22T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-23T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-24T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-25T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-26T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-27T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-28T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-29T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-30T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}], "monthLeaves": null, "employeeId": "60ae6db4-fe35-46ad-85d5-eb595f9b1425", "totalWorkTime": 0, "totalLeaveTime": 0, "totalOverTime": 0, "totalTripMinutes": 0, "monthLeaveDays": 0, "monthLeaveDaysUsed": 0, "defaultWorkHours": 0, "vacationDays": 0}, {"chineseName": "有信箱TST3", "employeeNumber": "D0000599", "schedulingStatus": 0, "totalMonthLeaveTime": 0, "totalScheduleLeaveTime": 72, "fatigueValue": 0, "schedulingStatusRangeMap": {"1": [{"startDate": "2025-06-01T00:00:00+00:00", "endDate": "2025-06-30T00:00:00+00:00"}]}, "empSchedulingStartDate": "2025-06-01T00:00:00+00:00", "empSchedulingEndDate": "2025-06-23T00:00:00+00:00", "supervisorSchedulingStartDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingEndDate": "0001-01-01T00:00:00+00:00", "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isSupervisorEditable": false, "calendars": [{"date": "2025-06-01T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 7, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-01T04:00:00+00:00", "restTimeEnd1": "2025-06-01T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 7, "cycleStatus": 2, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": null, "workOffTime": null, "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00004", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-05-31T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-02T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-02T04:00:00+00:00", "restTimeEnd1": "2025-06-02T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-02T01:00:00+00:00", "workOffTime": "2025-06-02T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-01T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-03T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 2, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-03T04:00:00+00:00", "restTimeEnd1": "2025-06-03T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 2, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-03T01:00:00+00:00", "workOffTime": "2025-06-03T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-02T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-04T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 3, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-04T04:00:00+00:00", "restTimeEnd1": "2025-06-04T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 3, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-04T01:30:00+00:00", "workOffTime": "2025-06-04T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-03T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-05T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 4, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-05T04:00:00+00:00", "restTimeEnd1": "2025-06-05T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 4, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-05T01:00:00+00:00", "workOffTime": "2025-06-05T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-04T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-06T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 5, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-06T04:00:00+00:00", "restTimeEnd1": "2025-06-06T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 5, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-06T00:30:00+00:00", "workOffTime": "2025-06-06T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-05T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-07T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 6, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": null, "restTimeEnd1": null, "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 6, "cycleStatus": 2, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": null, "workOffTime": null, "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00003", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-06T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-08T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 7, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-08T04:00:00+00:00", "restTimeEnd1": "2025-06-08T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 7, "cycleStatus": 2, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": null, "workOffTime": null, "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00004", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-07T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-09T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-09T04:00:00+00:00", "restTimeEnd1": "2025-06-09T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-09T01:00:00+00:00", "workOffTime": "2025-06-09T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-08T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-10T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 2, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-10T04:00:00+00:00", "restTimeEnd1": "2025-06-10T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 2, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-10T01:00:00+00:00", "workOffTime": "2025-06-10T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-09T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-11T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 3, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-11T04:00:00+00:00", "restTimeEnd1": "2025-06-11T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 3, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-11T01:30:00+00:00", "workOffTime": "2025-06-11T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-10T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-12T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 4, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-12T04:00:00+00:00", "restTimeEnd1": "2025-06-12T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 4, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-12T01:00:00+00:00", "workOffTime": "2025-06-12T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-11T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-13T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 5, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-13T04:00:00+00:00", "restTimeEnd1": "2025-06-13T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 5, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-13T00:30:00+00:00", "workOffTime": "2025-06-13T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-12T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-14T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 6, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": null, "restTimeEnd1": null, "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 6, "cycleStatus": 2, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": null, "workOffTime": null, "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00003", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-13T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-15T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 7, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-15T04:00:00+00:00", "restTimeEnd1": "2025-06-15T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 7, "cycleStatus": 2, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": null, "workOffTime": null, "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00004", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-14T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-16T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-16T04:00:00+00:00", "restTimeEnd1": "2025-06-16T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-16T01:00:00+00:00", "workOffTime": "2025-06-16T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-15T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-17T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 2, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-17T04:00:00+00:00", "restTimeEnd1": "2025-06-17T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 2, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-17T01:00:00+00:00", "workOffTime": "2025-06-17T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-16T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-18T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 3, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-18T04:00:00+00:00", "restTimeEnd1": "2025-06-18T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 3, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-18T01:30:00+00:00", "workOffTime": "2025-06-18T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-17T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-19T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 4, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-19T04:00:00+00:00", "restTimeEnd1": "2025-06-19T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 4, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-19T01:00:00+00:00", "workOffTime": "2025-06-19T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-18T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-20T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 5, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-20T04:00:00+00:00", "restTimeEnd1": "2025-06-20T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 5, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-20T00:30:00+00:00", "workOffTime": "2025-06-20T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-19T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-21T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 6, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": null, "restTimeEnd1": null, "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 6, "cycleStatus": 2, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": null, "workOffTime": null, "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00003", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-20T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-22T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 7, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-22T04:00:00+00:00", "restTimeEnd1": "2025-06-22T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 7, "cycleStatus": 2, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": null, "workOffTime": null, "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00004", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-21T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-23T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-23T04:00:00+00:00", "restTimeEnd1": "2025-06-23T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-23T01:00:00+00:00", "workOffTime": "2025-06-23T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-22T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-24T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 2, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-24T04:00:00+00:00", "restTimeEnd1": "2025-06-24T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 2, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-24T01:00:00+00:00", "workOffTime": "2025-06-24T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-23T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-25T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 3, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-25T04:00:00+00:00", "restTimeEnd1": "2025-06-25T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 3, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-25T01:30:00+00:00", "workOffTime": "2025-06-25T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-24T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-26T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 4, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-26T04:00:00+00:00", "restTimeEnd1": "2025-06-26T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 4, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-26T01:00:00+00:00", "workOffTime": "2025-06-26T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-25T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-27T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 5, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-27T04:00:00+00:00", "restTimeEnd1": "2025-06-27T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 5, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-27T00:30:00+00:00", "workOffTime": "2025-06-27T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-26T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-28T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 6, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": null, "restTimeEnd1": null, "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 6, "cycleStatus": 2, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": null, "workOffTime": null, "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00003", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-27T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-29T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 7, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-29T04:00:00+00:00", "restTimeEnd1": "2025-06-29T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 7, "cycleStatus": 2, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": null, "workOffTime": null, "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00004", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-28T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-30T00:00:00+00:00", "shiftId": "80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-30T04:00:00+00:00", "restTimeEnd1": "2025-06-30T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "王永慶正常班", "isWorkTimeChanged": false, "workOnTime": "2025-06-30T01:00:00+00:00", "workOffTime": "2025-06-30T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-29T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:49+00:00", "latestUpdaterName": "", "schedulingStatus": 1}], "monthLeaves": [], "employeeId": "ab5a2c57-78e7-4235-8c05-2f2447f9fee6", "totalWorkTime": 157.5, "totalLeaveTime": 0, "totalOverTime": 0, "totalTripMinutes": 0, "monthLeaveDays": 0, "monthLeaveDaysUsed": 0, "defaultWorkHours": 8, "vacationDays": 9}, {"chineseName": "林百浬", "employeeNumber": "E0000202", "schedulingStatus": 0, "totalMonthLeaveTime": 0, "totalScheduleLeaveTime": 0, "fatigueValue": 0, "schedulingStatusRangeMap": {"3": [{"startDate": "2025-06-01T00:00:00+00:00", "endDate": "2025-06-30T00:00:00+00:00"}]}, "empSchedulingStartDate": "0001-01-01T00:00:00+00:00", "empSchedulingEndDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingStartDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingEndDate": "0001-01-01T00:00:00+00:00", "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isSupervisorEditable": false, "calendars": [{"date": "2025-06-01T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-02T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-03T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-04T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-05T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-06T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-07T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-08T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-09T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-10T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-11T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-12T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-13T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-14T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-15T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-16T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-17T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-18T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-19T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-20T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-21T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-22T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-23T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-24T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-25T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-26T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-27T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-28T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-29T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-30T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}], "monthLeaves": null, "employeeId": "dd87266f-d365-4f21-87e0-67865e5df7d2", "totalWorkTime": 0, "totalLeaveTime": 0, "totalOverTime": 0, "totalTripMinutes": 0, "monthLeaveDays": 0, "monthLeaveDaysUsed": 0, "defaultWorkHours": 0, "vacationDays": 0}, {"chineseName": "曹小亮", "employeeNumber": "E0055556", "schedulingStatus": 0, "totalMonthLeaveTime": 0, "totalScheduleLeaveTime": 0, "fatigueValue": 0, "schedulingStatusRangeMap": {"3": [{"startDate": "2025-06-01T00:00:00+00:00", "endDate": "2025-06-30T00:00:00+00:00"}]}, "empSchedulingStartDate": "0001-01-01T00:00:00+00:00", "empSchedulingEndDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingStartDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingEndDate": "0001-01-01T00:00:00+00:00", "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isSupervisorEditable": false, "calendars": [{"date": "2025-06-01T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-02T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-03T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-04T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-05T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-06T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-07T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-08T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-09T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-10T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-11T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-12T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-13T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-14T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-15T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-16T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-17T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-18T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-19T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-20T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-21T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-22T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-23T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-24T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-25T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-26T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-27T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-28T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-29T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-30T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}], "monthLeaves": null, "employeeId": "42df71a2-291b-4c81-84e3-9e0b0087d969", "totalWorkTime": 0, "totalLeaveTime": 0, "totalOverTime": 0, "totalTripMinutes": 0, "monthLeaveDays": 0, "monthLeaveDaysUsed": 0, "defaultWorkHours": 0, "vacationDays": 0}, {"chineseName": "林鴉鴉", "employeeNumber": "E878794", "schedulingStatus": 0, "totalMonthLeaveTime": 0, "totalScheduleLeaveTime": 0, "fatigueValue": 0, "schedulingStatusRangeMap": {"3": [{"startDate": "2025-06-01T00:00:00+00:00", "endDate": "2025-06-30T00:00:00+00:00"}]}, "empSchedulingStartDate": "0001-01-01T00:00:00+00:00", "empSchedulingEndDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingStartDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingEndDate": "0001-01-01T00:00:00+00:00", "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isSupervisorEditable": false, "calendars": [{"date": "2025-06-01T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-02T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-03T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-04T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-05T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-06T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-07T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-08T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-09T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-10T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-11T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-12T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-13T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-14T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-15T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-16T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-17T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-18T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-19T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-20T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-21T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-22T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-23T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-24T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-25T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-26T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-27T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-28T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-29T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-30T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}], "monthLeaves": null, "employeeId": "d1f483aa-fee5-4f5d-a265-4582c82202e4", "totalWorkTime": 0, "totalLeaveTime": 0, "totalOverTime": 0, "totalTripMinutes": 0, "monthLeaveDays": 0, "monthLeaveDaysUsed": 0, "defaultWorkHours": 0, "vacationDays": 0}, {"chineseName": "測試人員", "employeeNumber": "E00043", "schedulingStatus": 0, "totalMonthLeaveTime": 0, "totalScheduleLeaveTime": 0, "fatigueValue": 0, "schedulingStatusRangeMap": {"3": [{"startDate": "2025-06-01T00:00:00+00:00", "endDate": "2025-06-30T00:00:00+00:00"}]}, "empSchedulingStartDate": "0001-01-01T00:00:00+00:00", "empSchedulingEndDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingStartDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingEndDate": "0001-01-01T00:00:00+00:00", "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isSupervisorEditable": false, "calendars": [{"date": "2025-06-01T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-02T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-03T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-04T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-05T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-06T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-07T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-08T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-09T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-10T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-11T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-12T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-13T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-14T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-15T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-16T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-17T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-18T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-19T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-20T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-21T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-22T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-23T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-24T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-25T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-26T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-27T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-28T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-29T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-30T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}], "monthLeaves": null, "employeeId": "af860956-7eb8-4049-b891-a1ae13ede892", "totalWorkTime": 0, "totalLeaveTime": 0, "totalOverTime": 0, "totalTripMinutes": 0, "monthLeaveDays": 0, "monthLeaveDaysUsed": 0, "defaultWorkHours": 0, "vacationDays": 0}, {"chineseName": "陳小名", "employeeNumber": "E0055555", "schedulingStatus": 0, "totalMonthLeaveTime": 0, "totalScheduleLeaveTime": 0, "fatigueValue": 0, "schedulingStatusRangeMap": {"3": [{"startDate": "2025-06-01T00:00:00+00:00", "endDate": "2025-06-30T00:00:00+00:00"}]}, "empSchedulingStartDate": "0001-01-01T00:00:00+00:00", "empSchedulingEndDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingStartDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingEndDate": "0001-01-01T00:00:00+00:00", "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isSupervisorEditable": false, "calendars": [{"date": "2025-06-01T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-02T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-03T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-04T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-05T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-06T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-07T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-08T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-09T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-10T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-11T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-12T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-13T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-14T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-15T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-16T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-17T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-18T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-19T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-20T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-21T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-22T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-23T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-24T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-25T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-26T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-27T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-28T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-29T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-30T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}], "monthLeaves": null, "employeeId": "5991cd48-0522-4edf-b721-585c298d14c2", "totalWorkTime": 0, "totalLeaveTime": 0, "totalOverTime": 0, "totalTripMinutes": 0, "monthLeaveDays": 0, "monthLeaveDaysUsed": 0, "defaultWorkHours": 0, "vacationDays": 0}, {"chineseName": "嚕米袋斯特", "employeeNumber": "F0000006", "schedulingStatus": 0, "totalMonthLeaveTime": 0, "totalScheduleLeaveTime": 0, "fatigueValue": 0, "schedulingStatusRangeMap": {"1": [{"startDate": "2025-06-01T00:00:00+00:00", "endDate": "2025-06-30T00:00:00+00:00"}]}, "empSchedulingStartDate": "2025-06-01T00:00:00+00:00", "empSchedulingEndDate": "2025-06-30T00:00:00+00:00", "supervisorSchedulingStartDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingEndDate": "0001-01-01T00:00:00+00:00", "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isSupervisorEditable": false, "calendars": [{"date": "2025-06-01T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-01T03:30:00+00:00", "restTimeEnd1": "2025-06-01T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-05-31T23:00:00+00:00", "workOffTime": "2025-06-01T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-05-31T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-02T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-02T03:30:00+00:00", "restTimeEnd1": "2025-06-02T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-01T23:00:00+00:00", "workOffTime": "2025-06-02T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-01T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-03T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-03T03:30:00+00:00", "restTimeEnd1": "2025-06-03T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-02T23:00:00+00:00", "workOffTime": "2025-06-03T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-02T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-04T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-04T03:30:00+00:00", "restTimeEnd1": "2025-06-04T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-03T23:00:00+00:00", "workOffTime": "2025-06-04T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-03T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-05T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-05T03:30:00+00:00", "restTimeEnd1": "2025-06-05T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-04T23:00:00+00:00", "workOffTime": "2025-06-05T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-04T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-06T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-06T03:30:00+00:00", "restTimeEnd1": "2025-06-06T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-05T23:00:00+00:00", "workOffTime": "2025-06-06T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-05T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-07T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-07T03:30:00+00:00", "restTimeEnd1": "2025-06-07T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-06T23:00:00+00:00", "workOffTime": "2025-06-07T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-06T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-08T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-08T03:30:00+00:00", "restTimeEnd1": "2025-06-08T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-07T23:00:00+00:00", "workOffTime": "2025-06-08T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-07T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-09T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-09T03:30:00+00:00", "restTimeEnd1": "2025-06-09T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-08T23:00:00+00:00", "workOffTime": "2025-06-09T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-08T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-10T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-10T03:30:00+00:00", "restTimeEnd1": "2025-06-10T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-09T23:00:00+00:00", "workOffTime": "2025-06-10T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-09T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-11T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-11T03:30:00+00:00", "restTimeEnd1": "2025-06-11T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-10T23:00:00+00:00", "workOffTime": "2025-06-11T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-10T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-12T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-12T03:30:00+00:00", "restTimeEnd1": "2025-06-12T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-11T23:00:00+00:00", "workOffTime": "2025-06-12T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-11T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-13T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-13T03:30:00+00:00", "restTimeEnd1": "2025-06-13T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-12T23:00:00+00:00", "workOffTime": "2025-06-13T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-12T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-14T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-14T03:30:00+00:00", "restTimeEnd1": "2025-06-14T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-13T23:00:00+00:00", "workOffTime": "2025-06-14T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-13T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-15T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-15T03:30:00+00:00", "restTimeEnd1": "2025-06-15T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-14T23:00:00+00:00", "workOffTime": "2025-06-15T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-14T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-16T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-16T03:30:00+00:00", "restTimeEnd1": "2025-06-16T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-15T23:00:00+00:00", "workOffTime": "2025-06-16T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-15T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-17T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-17T03:30:00+00:00", "restTimeEnd1": "2025-06-17T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-16T23:00:00+00:00", "workOffTime": "2025-06-17T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-16T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-18T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-18T03:30:00+00:00", "restTimeEnd1": "2025-06-18T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-17T23:00:00+00:00", "workOffTime": "2025-06-18T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-17T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-19T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-19T03:30:00+00:00", "restTimeEnd1": "2025-06-19T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-18T23:00:00+00:00", "workOffTime": "2025-06-19T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-18T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-20T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-20T03:30:00+00:00", "restTimeEnd1": "2025-06-20T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-19T23:00:00+00:00", "workOffTime": "2025-06-20T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-19T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-21T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-21T03:30:00+00:00", "restTimeEnd1": "2025-06-21T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-20T23:00:00+00:00", "workOffTime": "2025-06-21T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-20T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-22T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-22T03:30:00+00:00", "restTimeEnd1": "2025-06-22T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-21T23:00:00+00:00", "workOffTime": "2025-06-22T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-21T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-23T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-23T03:30:00+00:00", "restTimeEnd1": "2025-06-23T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-22T23:00:00+00:00", "workOffTime": "2025-06-23T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-22T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-24T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-24T03:30:00+00:00", "restTimeEnd1": "2025-06-24T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-23T23:00:00+00:00", "workOffTime": "2025-06-24T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-23T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-25T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-25T03:30:00+00:00", "restTimeEnd1": "2025-06-25T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-24T23:00:00+00:00", "workOffTime": "2025-06-25T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-24T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-26T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-26T03:30:00+00:00", "restTimeEnd1": "2025-06-26T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-25T23:00:00+00:00", "workOffTime": "2025-06-26T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-25T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-27T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-27T03:30:00+00:00", "restTimeEnd1": "2025-06-27T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-26T23:00:00+00:00", "workOffTime": "2025-06-27T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-26T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-28T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-28T03:30:00+00:00", "restTimeEnd1": "2025-06-28T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-27T23:00:00+00:00", "workOffTime": "2025-06-28T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-27T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-29T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-29T03:30:00+00:00", "restTimeEnd1": "2025-06-29T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-28T23:00:00+00:00", "workOffTime": "2025-06-29T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-28T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}, {"date": "2025-06-30T00:00:00+00:00", "shiftId": "6dc300d2-6ead-4d79-96e4-29a40b320139", "cycleSn": 1, "billingStatus": 0, "isBilling": false, "shiftSchedule": {"restTimeStart1": "2025-06-30T03:30:00+00:00", "restTimeEnd1": "2025-06-30T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "cycleSn": 1, "cycleStatus": 1, "colorCode": "#FE9C4E", "shiftScheduleName": "陳湘頤中班", "isWorkTimeChanged": false, "workOnTime": "2025-06-29T23:00:00+00:00", "workOffTime": "2025-06-30T10:00:00+00:00", "originalWorkOnTime": null, "originalWorkOffTime": null, "restMinutes": null}, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": [], "overtimeSheets": [], "partialSupport": [], "tripSheets": [], "selectShiftSchedule": true, "arrangeLeave": true, "isShiftRules": true, "adjustmentScheduleTime": true, "advanceLeave": true, "isEditable": true, "isAllLeaveRequestFormsApproved": null, "itemOptionId": "CY00001", "finalBilling": false, "clockedInTime": null, "dayStartTime": "2025-06-29T16:00:00+00:00", "latestUpdateTime": "2024-11-01T02:56:50+00:00", "latestUpdaterName": "", "schedulingStatus": 1}], "monthLeaves": [], "employeeId": "3c6b7f55-c8ee-4662-bad4-6a911739e052", "totalWorkTime": 285, "totalLeaveTime": 0, "totalOverTime": 0, "totalTripMinutes": 0, "monthLeaveDays": 0, "monthLeaveDaysUsed": 0, "defaultWorkHours": 8, "vacationDays": 0}, {"chineseName": "陳小豬", "employeeNumber": "F001000", "schedulingStatus": 0, "totalMonthLeaveTime": 0, "totalScheduleLeaveTime": 0, "fatigueValue": 0, "schedulingStatusRangeMap": {"3": [{"startDate": "2025-06-01T00:00:00+00:00", "endDate": "2025-06-30T00:00:00+00:00"}]}, "empSchedulingStartDate": "0001-01-01T00:00:00+00:00", "empSchedulingEndDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingStartDate": "0001-01-01T00:00:00+00:00", "supervisorSchedulingEndDate": "0001-01-01T00:00:00+00:00", "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isSupervisorEditable": false, "calendars": [{"date": "2025-06-01T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-02T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-03T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-04T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-05T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-06T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-07T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-08T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-09T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-10T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-11T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-12T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-13T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-14T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-15T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-16T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-17T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-18T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-19T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-20T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-21T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-22T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-23T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-24T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-25T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-26T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-27T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-28T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-29T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}, {"date": "2025-06-30T00:00:00+00:00", "shiftId": null, "cycleSn": 0, "billingStatus": 0, "isBilling": false, "shiftSchedule": null, "supportDeptId": null, "supportDeptName": null, "calendarEvent": null, "leaveSheets": null, "overtimeSheets": null, "partialSupport": null, "tripSheets": null, "selectShiftSchedule": false, "arrangeLeave": false, "isShiftRules": false, "adjustmentScheduleTime": false, "advanceLeave": false, "isEditable": false, "isAllLeaveRequestFormsApproved": null, "itemOptionId": null, "finalBilling": true, "clockedInTime": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "latestUpdateTime": "0001-01-01T00:00:00+00:00", "latestUpdaterName": null, "schedulingStatus": 3}], "monthLeaves": null, "employeeId": "366ca9f6-416d-4ec7-a3a0-db0b4b2dd8ed", "totalWorkTime": 0, "totalLeaveTime": 0, "totalOverTime": 0, "totalTripMinutes": 0, "monthLeaveDays": 0, "monthLeaveDaysUsed": 0, "defaultWorkHours": 0, "vacationDays": 0}], "startDate": "2025-06-01", "endDate": "2025-06-30", "shiftSchedules": {"monthLeaves": [{"shiftIds": ["80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "6dc300d2-6ead-4d79-96e4-29a40b320139"], "cycles": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "shiftScheduleId": "00000000-0000-0000-0000-000000000000", "shiftScheduleName": "月休", "colorCode": "#99999B", "remnant": null}, {"shiftIds": ["80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "6dc300d2-6ead-4d79-96e4-29a40b320139"], "cycles": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "shiftScheduleId": "00000000-0000-0000-0000-000000000001", "shiftScheduleName": "國定假日", "colorCode": "#99999B", "remnant": null}, {"shiftIds": ["80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "6dc300d2-6ead-4d79-96e4-29a40b320139"], "cycles": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "shiftScheduleId": "00000000-0000-0000-0000-000000000002", "shiftScheduleName": "休息日", "colorCode": "#99999B", "remnant": null}, {"shiftIds": ["80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004", "6dc300d2-6ead-4d79-96e4-29a40b320139"], "cycles": null, "dayStartTime": "0001-01-01T00:00:00+00:00", "shiftScheduleId": "00000000-0000-0000-0000-000000000003", "shiftScheduleName": "例假日", "colorCode": "#99999B", "remnant": null}], "shifts": [{"shiftIds": ["80c93ce7-e3c1-4acd-a0b4-13e6f0cdd004"], "cycles": [{"restTimeStart1": "2010-10-10T04:00:00+00:00", "restTimeEnd1": "2010-10-10T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "itemOptionId": "CY00001", "cycleSn": 3, "cycleStatus": 1, "workOnTime": "2010-10-10T01:30:00+00:00", "workOffTime": "2010-10-10T10:00:00+00:00"}, {"restTimeStart1": "2010-10-10T04:00:00+00:00", "restTimeEnd1": "2010-10-10T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "itemOptionId": "CY00001", "cycleSn": 5, "cycleStatus": 1, "workOnTime": "2010-10-10T00:30:00+00:00", "workOffTime": "2010-10-10T10:00:00+00:00"}, {"restTimeStart1": "2010-10-10T04:00:00+00:00", "restTimeEnd1": "2010-10-10T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "itemOptionId": "CY00004", "cycleSn": 7, "cycleStatus": 2, "workOnTime": null, "workOffTime": null}, {"restTimeStart1": "2010-10-10T04:00:00+00:00", "restTimeEnd1": "2010-10-10T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "itemOptionId": "CY00001", "cycleSn": 1, "cycleStatus": 1, "workOnTime": "2010-10-10T01:00:00+00:00", "workOffTime": "2010-10-10T10:00:00+00:00"}, {"restTimeStart1": null, "restTimeEnd1": null, "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "itemOptionId": "CY00003", "cycleSn": 6, "cycleStatus": 2, "workOnTime": null, "workOffTime": null}, {"restTimeStart1": "2010-10-10T04:00:00+00:00", "restTimeEnd1": "2010-10-10T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "itemOptionId": "CY00001", "cycleSn": 4, "cycleStatus": 1, "workOnTime": "2010-10-10T01:00:00+00:00", "workOffTime": "2010-10-10T10:00:00+00:00"}, {"restTimeStart1": "2010-10-10T04:00:00+00:00", "restTimeEnd1": "2010-10-10T05:30:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "itemOptionId": "CY00001", "cycleSn": 2, "cycleStatus": 1, "workOnTime": "2010-10-10T01:00:00+00:00", "workOffTime": "2010-10-10T10:00:00+00:00"}], "dayStartTime": "2010-10-09T16:00:00+00:00", "shiftScheduleId": "7f51d53e-2d6f-449a-a12b-447a1a341af6", "shiftScheduleName": "王永慶正常班", "colorCode": "#FE9C4E", "remnant": null}, {"shiftIds": ["6dc300d2-6ead-4d79-96e4-29a40b320139"], "cycles": [{"restTimeStart1": "2010-10-10T03:30:00+00:00", "restTimeEnd1": "2010-10-10T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "itemOptionId": "CY00001", "cycleSn": 1, "cycleStatus": 1, "workOnTime": "2010-10-09T23:00:00+00:00", "workOffTime": "2010-10-10T10:00:00+00:00"}], "dayStartTime": "2010-10-09T16:00:00+00:00", "shiftScheduleId": "2cffd57e-5d96-49e1-b609-2dbf21d53421", "shiftScheduleName": "陳湘頤中班", "colorCode": "#FE9C4E", "remnant": null}, {"shiftIds": ["6dc300d2-6ead-4d79-96e4-29a40b320139"], "cycles": [{"restTimeStart1": "2010-10-10T04:00:00+00:00", "restTimeEnd1": "2010-10-10T05:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "itemOptionId": "CY00003", "cycleSn": 1, "cycleStatus": 2, "workOnTime": null, "workOffTime": null}], "dayStartTime": "2010-10-09T16:00:00+00:00", "shiftScheduleId": "dc129ad6-32d2-4924-8df3-0fee0ef127c1", "shiftScheduleName": "放假班", "colorCode": "#F341A1", "remnant": null}, {"shiftIds": ["6dc300d2-6ead-4d79-96e4-29a40b320139"], "cycles": [{"restTimeStart1": "2010-10-10T01:00:00+00:00", "restTimeEnd1": "2010-10-10T01:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "itemOptionId": "CY00001", "cycleSn": 1, "cycleStatus": 1, "workOnTime": "2010-10-10T00:00:00+00:00", "workOffTime": "2010-10-10T04:00:00+00:00"}], "dayStartTime": "2010-10-09T17:00:00+00:00", "shiftScheduleId": "d7995341-8fcb-40ac-86fb-6844be31db38", "shiftScheduleName": "中午下班", "colorCode": "#E75718", "remnant": null}, {"shiftIds": ["6dc300d2-6ead-4d79-96e4-29a40b320139"], "cycles": [{"restTimeStart1": "2010-10-10T01:00:00+00:00", "restTimeEnd1": "2010-10-10T01:00:00+00:00", "restTimeStart2": null, "restTimeEnd2": null, "restTimeStart3": null, "restTimeEnd3": null, "itemOptionId": "CY00001", "cycleSn": 1, "cycleStatus": 1, "workOnTime": "2010-10-10T00:00:00+00:00", "workOffTime": "2010-10-10T05:30:00+00:00"}], "dayStartTime": "2010-10-09T17:00:00+00:00", "shiftScheduleId": "c4fe5345-93aa-4334-bb29-cfc8b2743249", "shiftScheduleName": "10點下班", "colorCode": "#07C1B6", "remnant": null}]}, "ShiftScheduleDashBoard": [{"ShiftScheduleId": "b7e795ed-d65a-46cb-9d38-e206ce6b719e", "ColorCode": "#07C1B6", "ShiftScheduleName": "早班班次A", "WorkTimes": [8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], "SupportTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Totals": [8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]}, {"ShiftScheduleId": "baec31f1-04ab-4b73-8106-268a935667be", "ColorCode": "#E75718", "ShiftScheduleName": "晚班班次A", "WorkTimes": [0, 4.5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "SupportTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Totals": [0, 4.5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, {"ShiftScheduleId": "bf4929ed-026d-4e3a-bc53-5634d5c9aea2", "ColorCode": "#E75718", "ShiftScheduleName": "AKI測試班", "WorkTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "SupportTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Totals": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, {"ShiftScheduleId": "49807da9-87cd-4bc5-8d8b-94e0ea6a50aa", "ColorCode": "#FE9C4E", "ShiftScheduleName": "幫你常日班", "WorkTimes": [0, 8, 8, 8, 8, 8, 0, 0, 8, 8, 8, 8, 8, 0, 0, 8, 8, 8, 8, 8, 0, 0, 8, 8, 8, 8, 8, 0, 0, 8], "SupportTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Totals": [0, 8, 8, 8, 8, 8, 0, 0, 8, 8, 8, 8, 8, 0, 0, 8, 8, 8, 8, 8, 0, 0, 8, 8, 8, 8, 8, 0, 0, 8]}, {"ShiftScheduleId": "e7366782-44fa-405e-a869-0a8a27334a7e", "ColorCode": "#FB7E1A", "ShiftScheduleName": "平日早班", "WorkTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "SupportTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Totals": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, {"ShiftScheduleId": "32f4bbcf-3e06-40da-a999-87dbcd221132", "ColorCode": "#300592", "ShiftScheduleName": "布萊恩測試1", "WorkTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "SupportTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Totals": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, {"ShiftScheduleId": "e60f545c-9f19-45a0-a28e-41d125d47a7f", "ColorCode": "#D81FE9", "ShiftScheduleName": "BBBBBBB", "WorkTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "SupportTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Totals": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, {"ShiftScheduleId": "122f8e0c-8558-459b-a5cf-d4465abd69f3", "ColorCode": "#FB7E1A", "ShiftScheduleName": "凱西常日班", "WorkTimes": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 0, 8, 8, 8, 8, 8, 0, 0, 8, 8, 8, 8, 8, 0, 0], "SupportTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Totals": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 0, 8, 8, 8, 8, 8, 0, 0, 8, 8, 8, 8, 8, 0, 0]}, {"ShiftScheduleId": "5f15fa0c-d4f9-4c01-a37c-f33de7403f0a", "ColorCode": "#92C31E", "ShiftScheduleName": "Erica常日班", "WorkTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "SupportTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Totals": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, {"ShiftScheduleId": "6232f808-bde8-46a1-84a0-79d85b878e16", "ColorCode": "#222240", "ShiftScheduleName": "腳本用班次0", "WorkTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "SupportTimes": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Totals": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}]}