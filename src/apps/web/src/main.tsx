import './index.css';
import '@mayo/mayo-ui-beta/dist/index2.css';
import App from './App.tsx';
import { createRoot } from 'react-dom/client';
import { StreamProvider } from './providers/Stream.tsx';
import { ThreadProvider } from './providers/Thread.tsx';
import { ParentDataProvider } from './providers/ParentData.tsx';
import { Toaster } from '@/components/ui/sonner';
import { NuqsAdapter } from 'nuqs/adapters/react-router/v6';
import { BrowserRouter } from 'react-router-dom';

createRoot(document.getElementById('root')!).render(
  <BrowserRouter>
    <NuqsAdapter>
      <ParentDataProvider>
        <ThreadProvider>
          <StreamProvider>
            <App />
          </StreamProvider>
        </ThreadProvider>
      </ParentDataProvider>
      <Toaster />
    </NuqsAdapter>
  </BrowserRouter>
);
