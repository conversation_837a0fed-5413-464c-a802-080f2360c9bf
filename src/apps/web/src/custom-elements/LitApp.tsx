import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from 'react-router-dom';
import { NuqsAdapter } from 'nuqs/adapters/react-router/v6';
import { ThreadProvider } from '../providers/Thread';
import { StreamProvider } from '../providers/Stream';
import { Toaster } from '../components/ui/sonner';
import App from '../App';

const LitApp: React.FC = () => {
  return (
    <BrowserRouter>
      <NuqsAdapter>
        <ThreadProvider>
          <StreamProvider>
            <App />
          </StreamProvider>
        </ThreadProvider>
        <Toaster />
      </NuqsAdapter>
    </BrowserRouter>
  );
};

export default LitApp;
