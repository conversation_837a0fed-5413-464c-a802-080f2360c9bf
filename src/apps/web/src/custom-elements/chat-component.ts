import { LitElement, css, html } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import { createRoot } from 'react-dom/client'
import type { Root } from 'react-dom/client'
import LitApp from './LitApp'
import React from 'react'
import styles from '../index.css?inline'; 
import { unsafeCSS } from 'lit'
/**
 * An example element.
 *
 * @slot - This element has a slot
 * @csspart button - The button
 */
@customElement('my-element')
export class MyElement extends LitElement {
  /**
   * Copy for the read the docs hint.
   */
  @property()
  docsHint = 'Click on the Vite and Lit logos to learn more'

  /**
   * The number of times the button has been clicked.
   */
  @property({ type: Number })
  count = 0

  /** React 入口 */
  private _root?: Root;

  firstUpdated() {
    // ● 1) 在 Shadow DOM 中放入一個容器
    const container = document.createElement('div');
    this.shadowRoot!.append(container);

    // ● 3) 建立 React Root 並首渲染
    this._root = createRoot(container);
    this._renderReact();
  }

  /** Lit 屬性更新時同步重新渲染 React 元件 */
  protected updated(changed: Map<string, unknown>) {
    if (changed.has('theme')) this._renderReact();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    this._root?.unmount();    // 清理
  }

  private _renderReact() {
    this._root!.render(
      React.createElement(LitApp)
    );
  }

  render() {
    return html``
  }

  static styles = css`${unsafeCSS(styles)}`
}

declare global {
  interface HTMLElementTagNameMap {
    'my-element': MyElement
  }
}
