/**
 * This file defines the tools available to the ReAct agent.
 * Tools are functions that the agent can use to interact with external systems or perform specific tasks.
 */
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import { MultiServerMCPClient } from "@langchain/mcp-adapters";
import { JsonSchema, jsonSchemaToZod } from "@n8n/json-schema-to-zod";
import { z } from "zod";
import { Tool } from "@langchain/core/tools";
/**
 * Tavily search tool configuration
 * This tool allows the agent to perform web searches using the Tavily API.
 */
const searchTavily = new TavilySearchResults({
  maxResults: 3,
});

const mcpClient = new MultiServerMCPClient({
  prefixToolNameWithServerName: false,
  mcpServers: {
    math: { // 此鍵名將用作工具名稱的前綴（如果 prefixToolNameWithServerName 為 true）
      transport: "stdio",
      command: "node",
      args: ["/Users/<USER>/Documents/文件/Mayo.nosync/mayo-projects/mcp-test/build/index.js"], // 指向已編譯的 JS 檔案
    },
  },
});

// 初始化 MCP 連接並載入工具
const tools = await mcpClient.getTools();


// 然後修改轉換代碼:
for (const tool of tools) {
  if (tool.schema && typeof tool.schema === 'object' && 'type' in tool.schema) {
    try {
        tool.schema = jsonSchemaToZod(tool.schema as unknown as JsonSchema);
    } catch (error) {
      console.warn(`無法轉換工具 ${tool.name} 的 schema:`, error);
      tool.schema = z.object({}).describe(tool.description || '');
    }
  }
}

console.log("MCP Tools Loaded:", tools);

/**
 * Export an array of all available tools
 * Add new tools to this array to make them available to the agent
 *
 * Note: You can create custom tools by implementing the Tool interface from @langchain/core/tools
 * and add them to this array.
 * See https://js.langchain.com/docs/how_to/custom_tools/#tool-function for more information.
 */
export const TOOLS = [searchTavily, ...tools] as Tool[];
