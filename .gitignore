# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
**/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz
.yarn/cache

# testing
/coverage

# next.js
/.next/
/out/

# production
/dist
**/dist
.turbo/

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
src/.env

# npm registry auth files
# FIX: 機敏資料，暫時允許上傳
# .npmrc.docker

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

credentials.json

# LangGraph API
.langgraph_api
# Documentation and Cursor Rules
_private/
.cursor/
.cursorrules
*.mdc
**/.cursor
.augment/
